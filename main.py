import os
import sys
import shutil
import random
import threading
import math
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from concurrent.futures import ThreadPoolExecutor
import requests
import webbrowser
# استيراد مكونات Pillow
from PIL import Image, ImageTk, ImageDraw, ImageFont, ImageOps
# استيراد مكتبة numpy
import numpy as np
# Temporarily removing cv2 import until we can install it properly
# Commented out due to missing modules in current MoviePy version
# from moviepy.video.fx.painting import painting
# from moviepy.video.fx.colorx import colorx
from moviepy.editor import (
    VideoFileClip, AudioFileClip, TextClip,
    CompositeVideoClip, concatenate_videoclips,
    ImageClip, ColorClip, VideoClip
)
import moviepy.video.fx.all as vfx

# ──────────────────────────────────────────────────────────────
# البحث عن FFmpeg وImageMagick بجانب exe تلقائياً
def find_binary(filename, default_path):
    exe_dir = os.path.dirname(sys.executable) if getattr(sys, "frozen", False) else os.path.dirname(__file__)
    local_path = os.path.join(exe_dir, filename)
    return local_path if os.path.exists(local_path) else default_path

ffmpeg_path = find_binary("ffmpeg.exe", r"C:\\ffmpeg\\ffmpeg.exe")
magick_path = find_binary("magick.exe", r"C:\\Program Files\\ImageMagick\\magick.exe")

import moviepy.config as cf
cf.IMAGEMAGICK_BINARY = magick_path
cf.FFMPEG_BINARY = ffmpeg_path
# تعيين حجم الذاكرة المؤقتة - تحسين للأداء
cf.CACHE_SIZE = 2048  # 2GB cache لتحسين الأداء

import math
from concurrent.futures import ThreadPoolExecutor


# --- Improved Pillow Resampling Import ---
# نحدد مرشح إعادة التحجيم الأكثر احتمالاً
PIL_RESAMPLING_FILTER = Image.BICUBIC # كقيمة افتراضية أخيرة

try:
    # محاولة استيراد Resampling enum الموجود في Pillow 9+
    from PIL.Image import Resampling
    PIL_RESAMPLING_FILTER = Resampling.LANCZOS
    # تعريف ثوابت إضافية للتوافق مع الكود القديم
    Image.LANCZOS = Resampling.LANCZOS
    Image.BICUBIC = Resampling.BICUBIC
    Image.BILINEAR = Resampling.BILINEAR
    Image.NEAREST = Resampling.NEAREST
    # تعريف ANTIALIAS كبديل لـ LANCZOS للتوافق مع الكود القديم
    Image.ANTIALIAS = Resampling.LANCZOS
except ImportError:
    # إذا لم يتم العثور على Resampling، نحاول إيجاد السمة مباشرة في Image
    if hasattr(Image, 'LANCZOS'):
        PIL_RESAMPLING_FILTER = Image.LANCZOS
    elif hasattr(Image, 'ANTIALIAS'): # في حال كانت مكتبة قديمة جداً لا تحتوي على LANCZOS
        PIL_RESAMPLING_FILTER = Image.ANTIALIAS
    else:
        # إذا لم يتم العثور على أي من هذه المرشحات، نستخدم BICUBIC كحل أخير
        print("Warning: Could not find LANCZOS or ANTIALIAS in Pillow, using BICUBIC for resampling.")



# تحديث متغيرات البيئة لاستخدام المسارات المكتشفة
os.environ["PATH"] += os.pathsep + os.path.dirname(ffmpeg_path)
os.environ["FFMPEG_BINARY"] = ffmpeg_path
os.environ["IMAGEIO_FFMPEG_EXE"] = ffmpeg_path
os.environ["IMAGEMAGICK_BINARY"] = magick_path

# محاولة إعداد PyDub و MoviePy. إذا فشلت بسبب عدم العثور على FFmpeg، أظهر رسالة خطأ واخرج.
try:
    # التحقق مما إذا كانت ملفات FFmpeg و ImageMagick موجودة بالفعل قبل المتابعة
    if not os.path.exists(ffmpeg_path):
         raise FileNotFoundError(f"ffmpeg.exe not found at: {ffmpeg_path}")
    if not os.path.exists(magick_path):
         raise FileNotFoundError(f"magick.exe not found at: {magick_path}")

    from pydub import AudioSegment

    AudioSegment.converter = ffmpeg_path
    AudioSegment.ffmpeg = ffmpeg_path
    AudioSegment.ffprobe = ffmpeg_path

    from moviepy.editor import (
        VideoFileClip, AudioFileClip,
        TextClip, CompositeVideoClip,
        concatenate_videoclips, ImageClip
    )
    import moviepy.video.fx.all as vfx

except FileNotFoundError as e:
    messagebox.showerror("خطأ في التهيئة", f"الملفات التنفيذية لـ FFmpeg أو ImageMagick مفقودة. تأكد من وجودهما بجانب البرنامج أو في المسارات المحددة.\nالتفاصيل: {e}")
    sys.exit(1)
except Exception as e:
    messagebox.showerror("خطأ في التهيئة", f"فشل إعداد المكتبات (PyDub/MoviePy). تأكد من تثبيتها بشكل صحيح ووجود FFmpeg/ImageMagick.\nالتفاصيل: {e}")
    sys.exit(1)


# ───────────────────────────────────────────────────────────────────────────────
# 2) المسارات الأساسية
# ───────────────────────────────────────────────────────────────────────────────
base = os.path.dirname(os.path.abspath(__file__))

# ⭐ احفظ الفيديو النهائي بجانب الـ exe أو بجانب main.py إذا لم يكن ملف exe
EXEC_DIR = os.path.dirname(sys.executable) if getattr(sys, "frozen", False) else base

OUT_DIR = os.path.join(base, "outputs")
AUDIO_DIR = os.path.join(OUT_DIR, "audio")
VIDEO_DIR = os.path.join(OUT_DIR, "video")
FONT_PATH = os.path.join(base, "font.ttf") # تأكد من وجود ملف font.ttf في نفس مسار السكربت

# متغيرات عامة للتحكم في الفيديو والخط
BACKGROUND_VIDEO_PATH = None
ARABIC_FONT_SIZE_FACTOR = 0.8  # معامل تقليص الخط (80% من الحجم الأصلي)
ARABIC_FONT_COLOR = '#FFD700'  # اللون الافتراضي (ذهبي)
VIDEO_ASPECT_RATIO = "16:9"  # نسبة العرض إلى الارتفاع الافتراضية
SHOW_WATERMARK = True  # إظهار العلامة المائية
WATERMARK_TEXT = "AL JUNAIDI CHARITY"  # النص الافتراضي للعلامة المائية
WATERMARK_OPACITY = 50  # شفافية العلامة المائية (50%)
WATERMARK_SIZE_FACTOR = 0.7  # معامل حجم العلامة المائية (70% من الحجم الأصلي)

# محاولة تحميل مسار الخلفية المحفوظ
try:
    background_path_file = os.path.join(EXEC_DIR, "background_path.txt")
    if os.path.exists(background_path_file):
        with open(background_path_file, "r", encoding="utf-8") as f:
            saved_path = f.read().strip()
            if saved_path and os.path.exists(saved_path):
                BACKGROUND_VIDEO_PATH = saved_path
                print(f"تم تحميل مسار الخلفية المحفوظ: {BACKGROUND_VIDEO_PATH}")
            else:
                print(f"مسار الخلفية المحفوظ غير صالح أو غير موجود: {saved_path}")
except Exception as e:
    print(f"خطأ في تحميل مسار الخلفية المحفوظ: {e}")

# أبعاد الفيديو المخرج - تحسين الجودة
VIDEO_RESOLUTIONS = {
    "360p (16:9)": (640, 360),     # 360p أفقي
    "720p (16:9)": (1280, 720),    # 720p HD أفقي
    "1080p (16:9)": (1920, 1080),  # 1080p Full HD أفقي
    "1440p (16:9)": (2560, 1440),  # 1440p 2K أفقي
    "2160p (16:9)": (3840, 2160),  # 2160p 4K أفقي
    "360p (9:16)": (360, 640),     # 360p طولي للهاتف المحمول
    "720p (9:16)": (720, 1280),    # 720p HD طولي للهاتف المحمول
    "1080p (9:16)": (1080, 1920),  # 1080p Full HD طولي للهاتف المحمول
    "1440p (9:16)": (1440, 2560),  # 1440p 2K طولي للهاتف المحمول
    "2160p (9:16)": (2160, 3840)   # 2160p 4K طولي للهاتف المحمول
}

# أبعاد الفيديو المخرج في الوضع السريع (دقة متوسطة)
FAST_VIDEO_RESOLUTIONS = {
    "360p (16:9)": (576, 324),     # 324p أفقي (أفضل من 270p)
    "720p (16:9)": (960, 540),     # 540p أفقي
    "1080p (16:9)": (1280, 720),   # 720p أفقي (مخفض من 1080p)
    "1440p (16:9)": (1600, 900),   # 900p أفقي (مخفض من 1440p)
    "2160p (16:9)": (1920, 1080),  # 1080p أفقي (مخفض من 4K)
    "360p (9:16)": (324, 576),     # 324p طولي للهاتف المحمول
    "720p (9:16)": (540, 960),     # 540p طولي للهاتف المحمول
    "1080p (9:16)": (720, 1280),   # 720p طولي (مخفض من 1080p)
    "1440p (9:16)": (900, 1600),   # 900p طولي (مخفض من 1440p)
    "2160p (9:16)": (1080, 1920)   # 1080p طولي (مخفض من 4K)
}
OUTPUT_RESOLUTION = "720p (16:9)"  # الدقة الافتراضية للمخرج - تحسين إلى 720p

# أضف هذا في بداية الملف للتحقق من الخط
if not os.path.exists(FONT_PATH):
    print("تحذير: ملف الخط غير موجود! سيتم استخدام الخط الافتراضي.")
    # استخدام خط النظام الافتراضي بدلاً من الخروج من البرنامج
    FONT_PATH = None

# ───────────────────────────────────────────────────────────────────────────────
# 2.1) قراءة مفتاح Pexels وإعدادات خادم MCP
# ───────────────────────────────────────────────────────────────────────────────
PEXELS_API_KEY = None
try:
    with open(os.path.join(base, "Pexels.txt"), "r") as f:
        PEXELS_API_KEY = f.read().strip()
    if not PEXELS_API_KEY:
         messagebox.showwarning("تحذير", "ملف Pexels.txt فارغ. لن يتم استخدام خلفيات من Pexels.")
except FileNotFoundError:
    messagebox.showwarning("تحذير", "ملف Pexels.txt مفقود. لن يتم استخدام خلفيات من Pexels.")
except Exception as e:
    messagebox.showwarning("تحذير", f"فشل قراءة ملف Pexels.txt: {e}. لن يتم استخدام خلفيات من Pexels.")

# ───────────────────────────────────────────────────────────────────────────────
# 2.2) قراءة إعدادات خادم MCP
# ───────────────────────────────────────────────────────────────────────────────
# إعدادات افتراضية لخادم MCP
MCP_SERVERS = []
CURRENT_MCP_SERVER = {
    "name": "الخادم الافتراضي",
    "quran_api": "https://api.alquran.cloud/v1",
    "audio_api": "https://everyayah.com/data",
    "is_default": True,
    "is_active": True,
    "performance": 100,
    "max_connections": 5,
    "timeout": 10
}

# قراءة إعدادات خادم MCP من الملف
try:
    import json
    mcp_config_path = os.path.join(base, "mcp_servers.json")
    if os.path.exists(mcp_config_path):
        with open(mcp_config_path, "r", encoding="utf-8") as f:
            mcp_data = json.load(f)
            MCP_SERVERS = mcp_data.get("servers", [])
            current_server_name = mcp_data.get("current_server", "الخادم الافتراضي")

            # البحث عن الخادم الحالي
            for server in MCP_SERVERS:
                if server.get("name") == current_server_name and server.get("is_active", True):
                    CURRENT_MCP_SERVER = server
                    break
    else:
        # إنشاء ملف إعدادات افتراضي إذا لم يكن موجوداً
        default_config = {
            "servers": [
                {
                    "name": "الخادم الافتراضي",
                    "quran_api": "https://api.alquran.cloud/v1",
                    "audio_api": "https://everyayah.com/data",
                    "is_default": True,
                    "is_active": True,
                    "performance": 100,
                    "max_connections": 5,
                    "timeout": 10
                },
                {
                    "name": "خادم MCP الجديد",
                    "quran_api": "https://api.quran.com/api/v4",
                    "audio_api": "https://verses.quran.com/audio",
                    "is_default": False,
                    "is_active": True,
                    "performance": 200,
                    "max_connections": 10,
                    "timeout": 5
                }
            ],
            "current_server": "الخادم الافتراضي"
        }
        with open(mcp_config_path, "w", encoding="utf-8") as f:
            json.dump(default_config, f, ensure_ascii=False, indent=4)
        MCP_SERVERS = default_config["servers"]
except Exception as e:
    print(f"تحذير: فشل قراءة إعدادات خادم MCP: {e}. سيتم استخدام الإعدادات الافتراضية.")


# ───────────────────────────────────────────────────────────────────────────────
# 3) ثوابت السور والقُرّاء والترجمات
# ───────────────────────────────────────────────────────────────────────────────
VERSE_COUNTS = {
    1: 7, 2: 286, 3: 200, 4: 176, 5: 120, 6: 165, 7: 206, 8: 75,
    9: 129, 10: 109, 11: 123, 12: 111, 13: 43, 14: 52, 15: 99, 16: 128,
    17: 111, 18: 110, 19: 98, 20: 135, 21: 112, 22: 78, 23: 118, 24: 64,
    25: 77, 26: 227, 27: 93, 28: 88, 29: 69, 30: 60, 31: 34, 32: 30,
    33: 73, 34: 54, 35: 45, 36: 83, 37: 182, 38: 88, 39: 75, 40: 85,
    41: 54, 42: 53, 43: 89, 44: 59, 45: 37, 46: 35, 47: 38, 48: 29,
    49: 18, 50: 45, 51: 60, 52: 49, 53: 62, 54: 55, 55: 78, 56: 96,
    57: 29, 58: 22, 59: 24, 60: 13, 61: 14, 62: 11, 63: 11, 64: 18,
    65: 12, 66: 12, 67: 30, 68: 52, 69: 52, 70: 44, 71: 28, 72: 28,
    73: 20, 74: 56, 75: 40, 76: 31, 77: 50, 78: 40, 79: 46, 80: 42,
    81: 29, 82: 19, 83: 36, 84: 25, 85: 22, 86: 17, 87: 19, 88: 26,
    89: 30, 90: 20, 91: 15, 92: 21, 93: 11, 94: 8, 95: 8, 96: 19,
    97: 5, 98: 8, 99: 8, 100: 11, 101: 11, 102: 8, 103: 3, 104: 9,
    105: 5, 106: 4, 107: 7, 108: 3, 109: 6, 110: 3, 111: 5, 112: 4,
    113: 5, 114: 6
}

SURAH_NAMES = [
    "الفاتحة", "البقرة", "آل عمران", "النساء", "المائدة", "الأنعام", "الأعراف", "الأنفال", "التوبة", "يونس",
    "هود", "يوسف", "الرعد", "إبراهيم", "الحجر", "النحل", "الإسراء", "الكهف", "مريم", "طه",
    "الأنبياء", "الحج", "المؤمنون", "النور", "الفرقان", "الشعراء", "النمل", "القصص", "العنكبوت", "الروم",
    "لقمان", "السجدة", "الأحزاب", "سبأ", "فاطر", "يس", "الصافات", "ص", "الزمر", "غافر",
    "فصلت", "الشورى", "الزخرف", "الدخان", "الجاثية", "الأحقاف", "محمد", "الفتح", "الحجرات", "ق",
    "الذاريات", "الطور", "النجم", "القمر", "الرحمن", "الواقعة", "الحديد", "المجادلة", "الحشر", "الممتحنة",
    "الصف", "الجمعة", "المنافقون", "التغابن", "الطلاق", "التحريم", "الملك", "القلم", "الحاقة", "المعارج",
    "نوح", "الجن", "المزمل", "مدثر", "القيامة", "الإنسان", "المرسلات", "النبأ", "النازعات", "عبس",
    "التكوير", "الإنفطار", "المطففين", "الإنشقاق", "البروج", "الطارق", "الأعلى", "الغاشية", "الفجر", "البلد",
    "الشمس", "الليل", "الضحى", "الشرح", "التين", "العلق", "القدر", "البينة", "الزلزلة", "العاديات",
    "القارعة", "التكاثر", "العصر", "الهمزة", "الفيل", "قريش", "الماعون", "الكوثر", "الكافرون", "النصر",
    "المسد", "الإخلاص", "الفلق", "الناس"
]

RECITERS_MAP = {
    "الشيخ عبدالباسط عبدالصمد": "AbdulSamad_64kbps_QuranExplorer.Com",
    "الشيخ عبدالباسط عبدالصمد (مرتل)": "Abdul_Basit_Murattal_64kbps",
    "الشيخ عبدالرحمن السديس": "Abdurrahmaan_As-Sudais_64kbps",
    "الشيخ ماهر المعيقلي": "Maher_AlMuaiqly_64kbps",
    "الشيخ محمد صديق المنشاوي (مجود)": "Minshawy_Mujawwad_64kbps",
    "الشيخ سعود الشريم": "Saood_ash-Shuraym_64kbps",
    "الشيخ مشاري العفاسي": "Alafasy_64kbps",
    "الشيخ محمود خليل الحصري": "Husary_64kbps",
    "الشيخ عبدالله الحذيفي": "Hudhaify_64kbps",
    "الشيخ أبو بكر الشاطري": "Abu_Bakr_Ash-Shaatree_128kbps",
    "الشيخ محمود علي البنا": "mahmoud_ali_al_banna_32kbps",
    "الشيخ ياسر الدوسري": "Yasser_Ad-Dussary_128kbps",
    "الشيخ سعد الغامدي": "Ghamadi_40kbps"
}
RECITERS_DISPLAY = list(RECITERS_MAP.keys())


# قائمة الترجمات المطلوبة (تمت إضافة الترجمات العربية: Buck و Muyassar)
DEFAULT_TRANSLATIONS = {
    "Arabic (Buck)": "ar.buck",  # ترجمة بُكثال العربية
    "Arabic (Muyassar)": "ar.muyassar",  # ترجمة الميسر (Quran for Kids/children style)
    "English (Sahih International)": "en.sahih",
    "French (Hamidullah)": "fr.hamidullah",
    "Italian (Piccardo)": "it.piccardo",
    "Turkish (Diyanet)": "tr.diyanet",
    "Russian (Kuliev)": "ru.kuliev"
}

# إضافة ترجمة الميسر إلى المتغير العام لضمان توفرها
MUYASSAR_TRANSLATION_ID = "ar.muyassar"

TRANSLATIONS_MAP = {} # سيتم ملؤها من API
TRANSLATIONS_DISPLAY = []

def fetch_translations():
    """جلب الترجمات المتاحة من خادم MCP الحالي"""
    global TRANSLATIONS_MAP, TRANSLATIONS_DISPLAY, CURRENT_MCP_SERVER

    # استخدام الترجمات الافتراضية كقيمة أولية
    TRANSLATIONS_MAP = {
        "English (Sahih International - en.sahih)": "en.sahih",
        "Arabic (Muyassar - ar.muyassar)": "ar.muyassar",
        "French (Hamidullah - fr.hamidullah)": "fr.hamidullah",
        "Italian (Piccardo - it.piccardo)": "it.piccardo",
        "Russian (Kuliev - ru.kuliev)": "ru.kuliev",
        "Turkish (Diyanet - tr.diyanet)": "tr.diyanet"
    }
    TRANSLATIONS_DISPLAY = list(TRANSLATIONS_MAP.keys())
    TRANSLATIONS_DISPLAY.sort()

    try:
        # الحصول على عنوان API ومهلة الاتصال من إعدادات الخادم الحالي
        quran_api = CURRENT_MCP_SERVER.get("quran_api", "https://api.alquran.cloud/v1")
        timeout = CURRENT_MCP_SERVER.get("timeout", 10)

        # بناء عنوان URL حسب نوع الخادم
        if "alquran.cloud" in quran_api:
            # محاولة جلب الترجمات من API مع تحديد مهلة زمنية
            resp = requests.get(f"{quran_api}/edition?format=text&type=translation", timeout=timeout)
            resp.raise_for_status()
            data = resp.json().get("data", [])

            # تنسيق أسماء الترجمات بشكل مناسب للعرض
            new_translations = {
                f"{t.get('englishName', t.get('name', 'Unknown'))} ({t.get('language', '??')} - {t['identifier']})": t['identifier']
                for t in data if t.get('format') == 'text' and t.get('type') == 'translation' and 'identifier' in t
            }

            # إضافة الترجمات الجديدة إلى القائمة الحالية
            if new_translations:
                TRANSLATIONS_MAP.update(new_translations)
                TRANSLATIONS_DISPLAY = list(TRANSLATIONS_MAP.keys())
                # ترتيب الترجمات أبجدياً لسهولة الاختيار
                TRANSLATIONS_DISPLAY.sort()
                print(f"تم جلب الترجمات بنجاح من خادم {CURRENT_MCP_SERVER.get('name')}")

        elif "quran.com" in quran_api:
            # استخدام API مختلف لـ quran.com
            resp = requests.get(f"{quran_api}/resources/translations", timeout=timeout)
            resp.raise_for_status()
            data = resp.json().get("translations", [])

            # تنسيق أسماء الترجمات بشكل مناسب للعرض
            new_translations = {
                f"{t.get('name', 'Unknown')} ({t.get('language_name', '??')} - {t['id']})": f"{t.get('language_name', 'en')}.{t['id']}"
                for t in data
            }

            # إضافة الترجمات الجديدة إلى القائمة الحالية
            if new_translations:
                TRANSLATIONS_MAP.update(new_translations)
                TRANSLATIONS_DISPLAY = list(TRANSLATIONS_MAP.keys())
                # ترتيب الترجمات أبجدياً لسهولة الاختيار
                TRANSLATIONS_DISPLAY.sort()
                print(f"تم جلب الترجمات بنجاح من خادم {CURRENT_MCP_SERVER.get('name')}")

        else:
            # محاولة استخدام الخادم الافتراضي كبديل
            try:
                fallback_url = "https://api.alquran.cloud/v1/edition?format=text&type=translation"
                resp = requests.get(fallback_url, timeout=10)
                resp.raise_for_status()
                data = resp.json().get("data", [])

                # تنسيق أسماء الترجمات بشكل مناسب للعرض
                new_translations = {
                    f"{t.get('englishName', t.get('name', 'Unknown'))} ({t.get('language', '??')} - {t['identifier']})": t['identifier']
                    for t in data if t.get('format') == 'text' and t.get('type') == 'translation' and 'identifier' in t
                }

                # إضافة الترجمات الجديدة إلى القائمة الحالية
                if new_translations:
                    TRANSLATIONS_MAP.update(new_translations)
                    TRANSLATIONS_DISPLAY = list(TRANSLATIONS_MAP.keys())
                    # ترتيب الترجمات أبجدياً لسهولة الاختيار
                    TRANSLATIONS_DISPLAY.sort()
                    print("تم جلب الترجمات بنجاح من الخادم الاحتياطي")
            except Exception as e:
                print(f"فشل جلب قائمة الترجمات من الخادم الاحتياطي: {e}. سيتم استخدام الترجمات الافتراضية.")

    except requests.exceptions.Timeout:
        # في حالة انتهاء المهلة الزمنية، استخدم الترجمات الافتراضية فقط
        print(f"انتهت المهلة الزمنية لجلب الترجمات من خادم {CURRENT_MCP_SERVER.get('name')}. سيتم استخدام الترجمات الافتراضية.")
    except requests.exceptions.ConnectionError:
        # في حالة عدم وجود اتصال بالإنترنت
        print("لا يوجد اتصال بالإنترنت. سيتم استخدام الترجمات الافتراضية.")
    except Exception as e:
        # استخدام الترجمات الافتراضية في حالة فشل الجلب
        print(f"فشل جلب قائمة الترجمات من خادم {CURRENT_MCP_SERVER.get('name')}: {e}. سيتم استخدام الترجمات الافتراضية.")


# ───────────────────────────────────────────────────────────────────────────────
# 4) audio helpers
# ───────────────────────────────────────────────────────────────────────────────

# دالة مساعدة لتحويل الوقت إلى تنسيق SRT
def format_time(milliseconds):
    """تحويل الوقت من ميلي ثانية إلى تنسيق SRT"""
    seconds = milliseconds / 1000
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = seconds % 60
    return f"{hours:02d}:{minutes:02d}:{int(seconds):02d},{int((seconds % 1) * 1000):03d}"

def create_srt_file(translations, durations, output_path):
    """إنشاء ملف ترجمة SRT"""
    current_time = 0
    with open(output_path, 'w', encoding='utf-8') as f:
        for i, (text, duration) in enumerate(zip(translations, durations), 1):
            start_time = format_time(current_time * 1000)
            end_time = format_time((current_time + duration) * 1000)
            f.write(f"{i}\n{start_time} --> {end_time}\n{text}\n\n")
            current_time += duration

def parse_srt_file(srt_path):
    """قراءة وتحليل ملف SRT واستخراج النصوص والتوقيتات"""
    if not os.path.exists(srt_path):
        print(f"ملف الترجمة غير موجود: {srt_path}")
        return []

    try:
        with open(srt_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # تقسيم المحتوى إلى مقاطع (كل مقطع يمثل ترجمة واحدة)
        segments = content.strip().split('\n\n')
        translations = []

        for segment in segments:
            lines = segment.strip().split('\n')
            if len(lines) >= 3:  # تأكد من أن المقطع يحتوي على الأقل على 3 أسطر (الرقم، التوقيت، النص)
                # استخراج النص (قد يكون على سطر واحد أو أكثر)
                text = '\n'.join(lines[2:])
                translations.append(text)

        return translations
    except Exception as e:
        print(f"خطأ في قراءة ملف الترجمة: {e}")
        return []

def clear_outputs():
    """تنظيف مجلدات الإخراج مع تجاهل الأخطاء عند حذف الملفات المستخدمة"""
    for d in (AUDIO_DIR, VIDEO_DIR):
        if os.path.isdir(d):
            try:
                # حذف الملفات واحدًا تلو الآخر بدلاً من حذف المجلد بأكمله
                for root_dir, dirs, files in os.walk(d):
                    for file in files:
                        try:
                            file_path = os.path.join(root_dir, file)
                            if os.path.exists(file_path):
                                os.remove(file_path)
                        except Exception as e:
                            # تجاهل الأخطاء عند حذف الملفات المستخدمة
                            print(f"تحذير: لا يمكن حذف الملف {file_path}: {e}")
            except Exception as e:
                print(f"تحذير: لا يمكن تنظيف المجلد {d}: {e}")

        # إنشاء المجلد إذا لم يكن موجودًا
        try:
            os.makedirs(d, exist_ok=True)
        except Exception as e:
            print(f"تحذير: لا يمكن إنشاء المجلد {d}: {e}")


# تم تعطيل دوال إزالة الصمت بناءً على طلب المستخدم
def detect_leading_silence(sound, thresh, chunk=10):
    # تم تعطيل هذه الدالة لعدم إزالة الصمت بين الآيات
    return 0

def detect_trailing_silence(sound, thresh, chunk=10):
    # تم تعطيل هذه الدالة لعدم إزالة الصمت بين الآيات
    return 0




def download_audio(reciter_id, surah, ayah, idx):
    """تنزيل الملف الصوتي للآية باستخدام خادم MCP الحالي - نسخة محسنة للتعامل مع مشاكل الاتصال"""
    os.makedirs(AUDIO_DIR, exist_ok=True)
    fn = f"{surah:03d}{ayah:03d}.mp3"
    out = os.path.join(AUDIO_DIR, f"part{idx}.mp3")



    # استخدام خادم MCP الحالي
    global CURRENT_MCP_SERVER
    audio_api = CURRENT_MCP_SERVER.get("audio_api", "https://everyayah.com/data")
    timeout = CURRENT_MCP_SERVER.get("timeout", 10)

    # قائمة بالخوادم البديلة للمحاولة
    alternative_servers = [
        "https://everyayah.com/data",
        "https://www.everyayah.com/data",
        "https://verses.quran.com/audio",
        "https://cdn.islamic.network/quran/audio"
    ]

    # إنشاء قائمة بجميع URLs المحتملة
    urls_to_try = []



    # إضافة URL الرئيسي أولاً للقراء الآخرين
    if "everyayah.com" in audio_api:
        urls_to_try.append((f"{audio_api}/{reciter_id}/{fn}", "الخادم الرئيسي"))
    elif "quran.com" in audio_api:
        # تنسيق مختلف لـ quran.com API
        urls_to_try.append((f"{audio_api}/{reciter_id}/verses/{surah}_{ayah}", "الخادم الرئيسي"))
    else:
        # استخدام التنسيق الافتراضي
        urls_to_try.append((f"{audio_api}/{reciter_id}/{fn}", "الخادم الرئيسي"))

    # إضافة URLs بديلة
    for server in alternative_servers:
        if server != audio_api:  # تجنب تكرار الخادم الرئيسي
            if "everyayah.com" in server:
                urls_to_try.append((f"{server}/{reciter_id}/{fn}", "خادم everyayah.com البديل"))
            elif "quran.com" in server:
                urls_to_try.append((f"{server}/{reciter_id}/verses/{surah}_{ayah}", "خادم quran.com البديل"))
            elif "islamic.network" in server:
                urls_to_try.append((f"{server}/{reciter_id}/{surah}/{ayah}.mp3", "خادم islamic.network البديل"))

    # إضافة URL بديل آخر لـ everyayah.com مع تغيير القارئ إذا كان القارئ الحالي غير متاح
    # ملاحظة: تم تعطيل القراء البديلين لتجنب الخلط بين الأصوات
    # alternative_reciters = ["Alafasy_64kbps", "Husary_64kbps", "Maher_AlMuaiqly_64kbps", "Minshawy_Murattal_64kbps"]
    # for alt_reciter in alternative_reciters:
    #     if alt_reciter != reciter_id:
    #         urls_to_try.append((f"https://everyayah.com/data/{alt_reciter}/{fn}", f"خادم everyayah.com مع قارئ بديل ({alt_reciter})"))

    # استخدام session واحدة لجميع الطلبات لتحسين الأداء
    session = requests.Session()

    # تعيين headers لتحسين الأداء
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': '*/*',
        'Connection': 'keep-alive'
    }

    # محاولة تنزيل الصوت من كل URL في القائمة
    last_error = None
    for url, server_desc in urls_to_try:
        try:
            print(f"محاولة تنزيل الصوت من {server_desc}: {url}")
            r = session.get(url, timeout=timeout, headers=headers)
            r.raise_for_status()

            # التحقق من أن الملف المنزل هو ملف صوتي صالح (حجمه أكبر من 1 كيلوبايت)
            if len(r.content) > 1024:
                with open(out, "wb") as f:
                    f.write(r.content)
                print(f"تم تنزيل الصوت بنجاح من {server_desc}")
                return out
            else:
                print(f"الملف المنزل من {server_desc} غير صالح (الحجم: {len(r.content)} بايت)")
                last_error = Exception(f"الملف المنزل من {server_desc} غير صالح (الحجم: {len(r.content)} بايت)")
        except Exception as e:
            print(f"خطأ في تنزيل الصوت من {server_desc}: {e}")
            last_error = e
            # الاستمرار في المحاولة مع الخادم التالي
            continue

    # إذا وصلنا إلى هنا، فقد فشلت جميع المحاولات
    # إنشاء ملف صوتي فارغ بدلاً من رفع استثناء
    try:
        print(f"فشلت جميع محاولات تنزيل الصوت للآية {surah}:{ayah}. سيتم إنشاء ملف صوتي فارغ.")

        # محاولة إنشاء ملف صوتي فارغ باستخدام pydub
        try:
            from pydub import AudioSegment
            empty_audio = AudioSegment.silent(duration=5000)  # 5 ثوانٍ من الصمت
            empty_audio.export(out, format="mp3")
            print(f"تم إنشاء ملف صوتي فارغ: {out}")
            return out
        except Exception as e:
            print(f"خطأ في إنشاء ملف صوتي فارغ باستخدام pydub: {e}")

            # إنشاء ملف MP3 فارغ يدوياً
            with open(out, "wb") as f:
                # كتابة header بسيط لملف MP3
                f.write(b"\xFF\xFB\x90\x04\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00")
            print(f"تم إنشاء ملف MP3 فارغ يدوياً: {out}")
            return out
    except Exception as e:
        print(f"خطأ في إنشاء ملف صوتي فارغ: {e}")
        # إذا فشل كل شيء، نرفع الاستثناء الأخير
        if last_error:
            raise Exception(f"فشلت جميع محاولات تنزيل الصوت للآية {surah}:{ayah}: {last_error}")
        else:
            raise Exception(f"فشلت جميع محاولات تنزيل الصوت للآية {surah}:{ayah}")


# ───────────────────────────────────────────────────────────────────────────────
# 4.1) نصوص وترجمات الآيات
# ───────────────────────────────────────────────────────────────────────────────
def get_ayah_text(surah, ayah):
    """الحصول على نص الآية باستخدام خادم MCP الحالي"""
    global CURRENT_MCP_SERVER
    quran_api = CURRENT_MCP_SERVER.get("quran_api", "https://api.alquran.cloud/v1")
    timeout = CURRENT_MCP_SERVER.get("timeout", 10)

    # بناء عنوان URL حسب نوع الخادم
    if "alquran.cloud" in quran_api:
        url = f"{quran_api}/ayah/{surah}:{ayah}/quran-uthmani"
        try:
            resp = requests.get(url, timeout=timeout)
            resp.raise_for_status()
            return resp.json()["data"]["text"]
        except Exception as e:
            print(f"خطأ في الحصول على نص الآية من الخادم الحالي: {e}")
            # محاولة استخدام الخادم الافتراضي كبديل
            try:
                fallback_url = f"https://api.alquran.cloud/v1/ayah/{surah}:{ayah}/quran-uthmani"
                resp = requests.get(fallback_url, timeout=10)
                resp.raise_for_status()
                return resp.json()["data"]["text"]
            except Exception as e2:
                print(f"خطأ في الحصول على نص الآية من الخادم الاحتياطي: {e2}")
                raise
    elif "quran.com" in quran_api:
        # تنسيق مختلف لـ quran.com API
        url = f"{quran_api}/verses/by_key/{surah}:{ayah}"
        try:
            resp = requests.get(url, timeout=timeout)
            resp.raise_for_status()
            # تنسيق مختلف للاستجابة
            return resp.json()["verse"]["text_uthmani"]
        except Exception as e:
            print(f"خطأ في الحصول على نص الآية من الخادم الحالي: {e}")
            # محاولة استخدام الخادم الافتراضي كبديل
            try:
                fallback_url = f"https://api.alquran.cloud/v1/ayah/{surah}:{ayah}/quran-uthmani"
                resp = requests.get(fallback_url, timeout=10)
                resp.raise_for_status()
                return resp.json()["data"]["text"]
            except Exception as e2:
                print(f"خطأ في الحصول على نص الآية من الخادم الاحتياطي: {e2}")
                raise
    else:
        # استخدام التنسيق الافتراضي
        url = f"{quran_api}/ayah/{surah}:{ayah}/quran-uthmani"
        try:
            resp = requests.get(url, timeout=timeout)
            resp.raise_for_status()
            return resp.json()["data"]["text"]
        except Exception as e:
            print(f"خطأ في الحصول على نص الآية من الخادم الحالي: {e}")
            # محاولة استخدام الخادم الافتراضي كبديل
            try:
                fallback_url = f"https://api.alquran.cloud/v1/ayah/{surah}:{ayah}/quran-uthmani"
                resp = requests.get(fallback_url, timeout=10)
                resp.raise_for_status()
                return resp.json()["data"]["text"]
            except Exception as e2:
                print(f"خطأ في الحصول على نص الآية من الخادم الاحتياطي: {e2}")
                raise


def get_ayah_translation(surah, ayah, translation_id):
    """الحصول على ترجمة الآية من API باستخدام خادم MCP الحالي - نسخة محسنة لتقليل التهنيج"""
    global CURRENT_MCP_SERVER
    quran_api = CURRENT_MCP_SERVER.get("quran_api", "https://api.alquran.cloud/v1")
    timeout = 3  # تقليل مهلة الانتظار أكثر لتسريع العملية

    # استخدام كاش محلي لتخزين الترجمات
    # إنشاء مفتاح فريد للترجمة
    cache_key = f"{surah}:{ayah}:{translation_id}"

    # التحقق من وجود الترجمة في الكاش
    if hasattr(get_ayah_translation, "cache") and cache_key in get_ayah_translation.cache:
        return get_ayah_translation.cache[cache_key]

    # إنشاء كاش إذا لم يكن موجوداً
    if not hasattr(get_ayah_translation, "cache"):
        get_ayah_translation.cache = {}

    # تحديث الواجهة لمنع التهنيج
    try:
        root.update_idletasks()
    except:
        pass

    # استخدام session واحدة لجميع الطلبات لتحسين الأداء
    if not hasattr(get_ayah_translation, "session"):
        get_ayah_translation.session = requests.Session()

    session = get_ayah_translation.session

    # تعيين headers لتحسين الأداء
    headers = {
        'User-Agent': 'Mozilla/5.0',
        'Accept': 'application/json',
        'Connection': 'keep-alive'
    }

    # تحديد URL الرئيسي والاحتياطي
    main_url = None
    fallback_url = None

    # معالجة خاصة لترجمة الميسر العربية
    if translation_id == "ar.muyassar":
        if "alquran.cloud" in quran_api:
            main_url = f"{quran_api}/ayah/{surah}:{ayah}/ar.muyassar"
            fallback_url = f"https://api.alquran.cloud/v1/ayah/{surah}:{ayah}/ar.muyassar"
        elif "quran.com" in quran_api:
            main_url = f"{quran_api}/verses/by_key/{surah}:{ayah}?translations=131"  # 131 هو معرف ترجمة الميسر في quran.com
            fallback_url = f"https://api.alquran.cloud/v1/ayah/{surah}:{ayah}/ar.muyassar"
        else:
            main_url = f"{quran_api}/ayah/{surah}:{ayah}/ar.muyassar"
            fallback_url = f"https://api.alquran.cloud/v1/ayah/{surah}:{ayah}/ar.muyassar"
    else:
        # للترجمات الأخرى
        if "alquran.cloud" in quran_api:
            main_url = f"{quran_api}/ayah/{surah}:{ayah}/{translation_id}"
            fallback_url = f"https://api.alquran.cloud/v1/ayah/{surah}:{ayah}/{translation_id}"
        elif "quran.com" in quran_api:
            # تحويل معرف الترجمة إلى معرف quran.com
            translation_map = {
                "en.sahih": "20",  # Sahih International
                "fr.hamidullah": "31",  # Hamidullah
                "it.piccardo": "33",  # Piccardo
                "ru.kuliev": "45",  # Kuliev
                "tr.diyanet": "77"   # Diyanet
            }
            quran_com_id = translation_map.get(translation_id, "20")
            main_url = f"{quran_api}/verses/by_key/{surah}:{ayah}?translations={quran_com_id}"
            fallback_url = f"https://api.alquran.cloud/v1/ayah/{surah}:{ayah}/{translation_id}"
        else:
            main_url = f"{quran_api}/ayah/{surah}:{ayah}/{translation_id}"
            fallback_url = f"https://api.alquran.cloud/v1/ayah/{surah}:{ayah}/{translation_id}"

    # محاولة الحصول على الترجمة من الخادم الرئيسي
    result = None
    try:
        resp = session.get(main_url, timeout=timeout, headers=headers)
        resp.raise_for_status()

        # معالجة الاستجابة حسب نوع الخادم
        if "quran.com" in quran_api:
            translations = resp.json()["verse"]["translations"]
            if translations and len(translations) > 0:
                result = translations[0]["text"]
            else:
                raise Exception("لم يتم العثور على الترجمة المطلوبة")
        else:
            result = resp.json()["data"]["text"]
    except Exception as e:
        # تحديث الواجهة لمنع التهنيج
        try:
            root.update_idletasks()
        except:
            pass

        # محاولة استخدام الخادم الاحتياطي
        try:
            resp = session.get(fallback_url, timeout=timeout, headers=headers)
            resp.raise_for_status()

            # معالجة الاستجابة من الخادم الاحتياطي
            if "quran.com" in fallback_url:
                translations = resp.json()["verse"]["translations"]
                if translations and len(translations) > 0:
                    result = translations[0]["text"]
                else:
                    raise Exception("لم يتم العثور على الترجمة المطلوبة")
            else:
                result = resp.json()["data"]["text"]
        except Exception as e2:
            # تحديث الواجهة لمنع التهنيج
            try:
                root.update_idletasks()
            except:
                pass

            # إذا فشلت جميع المحاولات، نعيد نص الآية الأصلي أو نص بديل
            if translation_id.startswith("ar."):
                try:
                    result = get_ayah_text(surah, ayah)
                except:
                    result = f"ترجمة الآية {surah}:{ayah} غير متوفرة"
            else:
                result = f"Translation for verse {surah}:{ayah} not available"

    # تخزين النتيجة في الكاش
    if result:
        get_ayah_translation.cache[cache_key] = result

    # تحرير الذاكرة بشكل دوري
    if len(get_ayah_translation.cache) > 1000:  # إذا تجاوز الكاش 1000 عنصر
        # حذف نصف العناصر الأقدم
        keys_to_delete = list(get_ayah_translation.cache.keys())[:500]
        for key in keys_to_delete:
            del get_ayah_translation.cache[key]

    return result


def wrap_text(text, per_char_line_limit):
    """Wraps text based on approximate character limit per line."""
    words = text.split()
    lines = []
    current_line_words = []
    current_line_char_count = 0

    for word in words:
        # Add 1 for the space after the word
        if current_line_char_count + len(word) + 1 > per_char_line_limit and current_line_words:
            lines.append(" ".join(current_line_words))
            current_line_words = [word]
            current_line_char_count = len(word)
        else:
            current_line_words.append(word)
            current_line_char_count += len(word) + 1 # Add 1 for the space

    # Add the last line
    if current_line_words:
        lines.append(" ".join(current_line_words))

    return "\n".join(lines)




def make_text_clip(arabic, english, duration, ayah_number=None, background_video_path=None):
    """Creates a single text clip with Arabic or English text centered with Islamic decorative elements."""
    try:
        # استخدام الأبعاد المحددة حسب نسبة العرض المختارة
        global OUTPUT_RESOLUTION, VIDEO_RESOLUTIONS, BACKGROUND_VIDEO_PATH

        # استخدام المتغير العام إذا لم يتم تمرير مسار الخلفية
        if background_video_path is None:
            background_video_path = BACKGROUND_VIDEO_PATH

        # طباعة مسار الخلفية للتشخيص
        print(f"استخدام مسار الخلفية: {background_video_path}")

        # الحصول على الأبعاد من القاموس
        if OUTPUT_RESOLUTION in VIDEO_RESOLUTIONS:
            VIDEO_WIDTH, VIDEO_HEIGHT = VIDEO_RESOLUTIONS[OUTPUT_RESOLUTION]
        else:
            # استخدام الأبعاد الافتراضية إذا لم تكن النسبة موجودة
            VIDEO_WIDTH, VIDEO_HEIGHT = 1280, 720

        # إنشاء الخلفية (فيديو أو خلفية سوداء)
        if background_video_path and os.path.exists(background_video_path):
            try:
                print(f"جاري تحميل فيديو الخلفية من: {background_video_path}")

                # تحميل الفيديو كخلفية مع تعطيل التحميل المسبق لتجنب تجميد الواجهة
                # إضافة خيارات لتقليل استهلاك الذاكرة
                # استخدام try/except للتعامل مع الإصدارات المختلفة من MoviePy
                try:
                    # محاولة استخدام معلمة logger (للإصدارات الحديثة)
                    video_clip = VideoFileClip(
                        background_video_path,
                        audio=False,
                        target_resolution=(VIDEO_HEIGHT, VIDEO_WIDTH),
                        fps_source="fps", # استخدام معدل الإطارات الأصلي
                        verbose=False,    # تعطيل الرسائل المطولة
                        logger=None       # تعطيل التسجيل
                    )
                except Exception as e:
                    print(f"خطأ في تحميل الفيديو (الطريقة الأولى): {e}")
                    # إذا فشلت، جرب بدون معلمة logger (للإصدارات القديمة)
                    try:
                        video_clip = VideoFileClip(
                            background_video_path,
                            audio=False,
                            target_resolution=(VIDEO_HEIGHT, VIDEO_WIDTH),
                            fps_source="fps", # استخدام معدل الإطارات الأصلي
                            verbose=False     # تعطيل الرسائل المطولة
                        )
                    except Exception as e2:
                        print(f"خطأ في تحميل الفيديو (الطريقة الثانية): {e2}")
                        # محاولة أخيرة بأقل الخيارات
                        video_clip = VideoFileClip(
                            background_video_path,
                            audio=False
                        )

                # طباعة معلومات الفيديو للتشخيص
                print(f"معلومات الفيديو: الحجم={video_clip.size}, المدة={video_clip.duration}, معدل الإطارات={video_clip.fps}")

                # تحديد مدة الفيديو المطلوبة
                if video_clip.duration >= duration:
                    # إذا كان الفيديو أطول من المدة المطلوبة، نأخذ جزء منه فقط
                    background = video_clip.subclip(0, duration)
                    print(f"تم استخدام جزء من الفيديو (0-{duration} ثانية)")
                else:
                    # إذا كان الفيديو أقصر، نستخدم ما هو متاح منه فقط
                    # ونكرره إذا لزم الأمر (بدون تكرار كامل للتقليل من استهلاك الذاكرة)
                    background = video_clip.loop(duration=duration)
                    print(f"تم تكرار الفيديو ليناسب المدة المطلوبة ({duration} ثانية)")

                # تحرير الذاكرة بعد تحميل الفيديو
                import gc
                gc.collect(2)

                # تأكد من أن الفيديو بالحجم المناسب
                if background.size != (VIDEO_WIDTH, VIDEO_HEIGHT):
                    print(f"تغيير حجم الفيديو من {background.size} إلى {(VIDEO_WIDTH, VIDEO_HEIGHT)}")
                    # استخدام معلمات تحسين الأداء لتغيير الحجم
                    if GPU_TYPE == 'nvidia':
                        # استخدام تسريع NVIDIA CUDA إذا كان متاحًا
                        try:
                            background = background.resize(height=VIDEO_HEIGHT, width=VIDEO_WIDTH,
                                                          use_cuda=True,
                                                          interpolation='bicubic')
                            print("تم استخدام تسريع NVIDIA CUDA لتغيير حجم الفيديو")
                        except Exception as e:
                            print(f"فشل استخدام تسريع NVIDIA CUDA: {e}")
                            background = background.resize(height=VIDEO_HEIGHT, width=VIDEO_WIDTH)
                    else:
                        # استخدام الطريقة العادية
                        background = background.resize(height=VIDEO_HEIGHT, width=VIDEO_WIDTH)

                print(f"تم تحميل فيديو الخلفية بنجاح")

                # التحقق من أن الخلفية تم إنشاؤها بشكل صحيح
                if background is None:
                    print("تحذير: الخلفية لا تزال None بعد التحميل، سيتم إنشاء خلفية سوداء بدلاً منها")
                    # إنشاء خلفية سوداء كبديل
                    from moviepy.editor import ColorClip
                    background = ColorClip(
                        size=(VIDEO_WIDTH, VIDEO_HEIGHT),
                        color=(0, 0, 0)  # أسود
                    ).set_duration(duration)

            except Exception as e:
                print(f"خطأ في تحميل فيديو الخلفية: {e}")
                # استخدام خلفية سوداء كبديل في حالة الخطأ
                try:
                    # استخدام ColorClip من moviepy.editor
                    from moviepy.editor import ColorClip
                    background = ColorClip(
                        size=(VIDEO_WIDTH, VIDEO_HEIGHT),
                        color=(0, 0, 0)  # أسود
                    ).set_duration(duration)
                    print("تم إنشاء خلفية سوداء بنجاح باستخدام ColorClip")
                except Exception as e2:
                    print(f"خطأ في إنشاء خلفية سوداء: {e2}")
                    # استخدام ImageClip كبديل
                    try:
                        # إنشاء صورة سوداء باستخدام PIL
                        from PIL import Image
                        black_image = Image.new('RGB', (VIDEO_WIDTH, VIDEO_HEIGHT), color=(0, 0, 0))
                        # تحويل الصورة إلى مصفوفة NumPy
                        import numpy as np
                        black_array = np.array(black_image)
                        # إنشاء ImageClip من المصفوفة
                        background = ImageClip(black_array).set_duration(duration)
                        print("تم إنشاء خلفية سوداء بنجاح باستخدام ImageClip")
                    except Exception as e3:
                        print(f"خطأ في إنشاء خلفية سوداء باستخدام ImageClip: {e3}")
                        # استخدام خلفية افتراضية
                        background = None
        else:
            print(f"استخدام خلفية سوداء (لم يتم تحديد فيديو أو الملف غير موجود): {background_video_path}")
            # استخدام خلفية سوداء إذا لم يتم تحديد فيديو
            try:
                # استخدام ColorClip من moviepy.editor
                from moviepy.editor import ColorClip
                background = ColorClip(
                    size=(VIDEO_WIDTH, VIDEO_HEIGHT),
                    color=(0, 0, 0)  # أسود
                ).set_duration(duration)
                print("تم إنشاء خلفية سوداء بنجاح باستخدام ColorClip")
            except Exception as e:
                print(f"خطأ في إنشاء خلفية سوداء: {e}")
                # استخدام ImageClip كبديل
                try:
                    # إنشاء صورة سوداء باستخدام PIL
                    from PIL import Image
                    black_image = Image.new('RGB', (VIDEO_WIDTH, VIDEO_HEIGHT), color=(0, 0, 0))
                    # تحويل الصورة إلى مصفوفة NumPy
                    import numpy as np
                    black_array = np.array(black_image)
                    # إنشاء ImageClip من المصفوفة
                    background = ImageClip(black_array).set_duration(duration)
                    print("تم إنشاء خلفية سوداء بنجاح باستخدام ImageClip")
                except Exception as e2:
                    print(f"خطأ في إنشاء خلفية سوداء باستخدام ImageClip: {e2}")
                    # استخدام خلفية افتراضية
                    background = None

        # استخدم Arial فقط بدون أي زخارف أو تأثيرات

        if arabic:
            # استخدام المتغيرات العامة لحجم ولون الخط ونسبة العرض
            global ARABIC_FONT_SIZE_FACTOR, ARABIC_FONT_COLOR, VIDEO_ASPECT_RATIO

            # تكييف حجم الخط حسب نسبة العرض إلى الارتفاع
            base_size = VIDEO_HEIGHT * 0.22

            # تعديل حجم الخط حسب نسبة العرض إلى الارتفاع
            if VIDEO_ASPECT_RATIO == "16:9":
                # نسبة عرض قياسية - استخدام الحجم الأساسي
                aspect_factor = 1.0
            elif VIDEO_ASPECT_RATIO == "4:3":
                # نسبة عرض أقل - تكبير الخط قليلاً
                aspect_factor = 1.1
            elif VIDEO_ASPECT_RATIO == "9:16":
                # نسبة عرض الهاتف المحمول - تصغير الخط
                aspect_factor = 0.8
            elif VIDEO_ASPECT_RATIO == "1:1":
                # نسبة مربعة - تكبير الخط
                aspect_factor = 1.2
            else:
                # نسبة أخرى - استخدام الحجم الأساسي
                aspect_factor = 1.0

            # حساب الحجم النهائي للخط
            ARABIC_FONT_SIZE = int(base_size * ARABIC_FONT_SIZE_FACTOR * aspect_factor)
            arabic_text = arabic

            try:
                # إعداد نص الآية بالعرض الكامل مع سماكة أكبر وظل
                # إضافة مسافة إضافية من الحواف (10 بيكسل)
                text_padding = 10

                # حساب العرض المتاح للنص مع مراعاة المسافة الإضافية
                available_width = int(VIDEO_WIDTH * 0.98) - (text_padding * 2)

                # إنشاء نص الظل أولاً (نفس النص ولكن باللون الأسود)
                shadow_clip = TextClip(
                    arabic_text,
                    font=FONT_PATH,  # استخدام ملف الخط المخصص
                    fontsize=ARABIC_FONT_SIZE,
                    color='black',  # لون الظل أسود
                    method='caption',
                    size=(available_width, None),  # السماح بارتفاع تلقائي مع مراعاة المسافة
                    align='center',
                    stroke_color='black',  # لون حدود الظل
                    stroke_width=0.8  # تقليل سماكة الحدود بشكل أكبر (كانت 1.5)
                )

                # إنشاء النص الرئيسي
                arabic_main = TextClip(
                    arabic_text,
                    font=FONT_PATH,  # استخدام ملف الخط المخصص
                    fontsize=ARABIC_FONT_SIZE,
                    color=ARABIC_FONT_COLOR,  # استخدام اللون المختار
                    method='caption',
                    size=(available_width, None),  # السماح بارتفاع تلقائي مع مراعاة المسافة
                    align='center',
                    stroke_color=ARABIC_FONT_COLOR,  # لون الحدود نفس لون النص
                    stroke_width=0.8  # تقليل سماكة الحدود بشكل أكبر (كانت 1.5)
                )

                # حساب ارتفاع نص الآية
                main_h = arabic_main.size[1] if hasattr(arabic_main, 'size') else int(VIDEO_HEIGHT * 0.5)
                # توسيط النص في منتصف الشاشة تماماً مع إضافة مسافة إضافية من الأعلى والأسفل
                ayah_y = int((VIDEO_HEIGHT - main_h) / 2)

                # حساب موقع الظل (إزاحة بسيطة للأسفل واليمين)
                shadow_offset = 3  # مقدار إزاحة الظل

                # إضافة مسافة إضافية من الأعلى والأسفل (10 بيكسل)
                vertical_padding = 10

                # تعديل موضع النص مع مراعاة المسافة الإضافية
                adjusted_ayah_y = ayah_y + vertical_padding

                # تعيين مدة العرض للظل والنص
                shadow_clip = shadow_clip.set_duration(duration)
                arabic_main = arabic_main.set_duration(duration)

                # توسيط الظل والنص في الشاشة مع مراعاة المسافة الإضافية
                shadow_clip = shadow_clip.set_position(('center', adjusted_ayah_y + shadow_offset))
                arabic_main = arabic_main.set_position(('center', adjusted_ayah_y))

                # إنشاء قائمة المقاطع للفيديو النهائي (الظل أولاً ثم النص فوقه)
                clips = [background, shadow_clip, arabic_main]

                # إضافة العلامة المائية المخصصة إذا كان الخيار مفعل
                global SHOW_WATERMARK, WATERMARK_TEXT, WATERMARK_OPACITY, WATERMARK_SIZE_FACTOR
                if SHOW_WATERMARK:
                    try:
                        # إنشاء صورة للعلامة المائية باستخدام PIL
                        from PIL import Image, ImageDraw, ImageFont

                        # تحديد حجم العلامة المائية بناءً على نسبة العرض
                        if VIDEO_ASPECT_RATIO == "16:9":
                            # للفيديو الأفقي، نستخدم عرض أكبر وارتفاع أقل
                            watermark_width = int(VIDEO_WIDTH * 0.8)
                            watermark_height = int(VIDEO_HEIGHT * 0.1)
                        else:  # 9:16
                            # للفيديو الطولي، نستخدم عرض أقل وارتفاع أكبر
                            watermark_width = int(VIDEO_WIDTH * 0.9)
                            watermark_height = int(VIDEO_HEIGHT * 0.08)

                        # إنشاء صورة شفافة
                        watermark_img = Image.new('RGBA', (watermark_width, watermark_height), (0, 0, 0, 0))

                        # إنشاء قلم رسم
                        draw = ImageDraw.Draw(watermark_img)

                        # تحديد حجم الخط بناءً على معامل الحجم ونسبة العرض
                        base_font_size = int(VIDEO_HEIGHT * 0.07)
                        font_size = int(base_font_size * WATERMARK_SIZE_FACTOR)  # تطبيق معامل الحجم

                        # تحديد الخط
                        try:
                            font = ImageFont.truetype("arial.ttf", font_size)
                        except:
                            font = ImageFont.load_default()

                        # حساب موقع النص لتوسيطه
                        text_width, text_height = draw.textsize(WATERMARK_TEXT, font=font) if hasattr(draw, 'textsize') else (watermark_width // 2, watermark_height // 2)
                        position = ((watermark_width - text_width) // 2, (watermark_height - text_height) // 2)

                        # تحديد مستوى الشفافية (0-255)
                        opacity = int(WATERMARK_OPACITY * 2.55)  # تحويل من نسبة مئوية إلى قيمة بين 0-255

                        # رسم النص باللون الأبيض مع الشفافية المحددة
                        draw.text(position, WATERMARK_TEXT, fill=(255, 255, 255, opacity), font=font)

                        # تحويل الصورة إلى ImageClip
                        watermark_clip = ImageClip(np.array(watermark_img))

                        # وضع العلامة المائية في أسفل الفيديو مع ترك مسافة من الحافة
                        watermark_y = int(VIDEO_HEIGHT * 0.9)  # 90% من ارتفاع الفيديو (أقرب للأسفل)

                        # التأكد من عدم تداخل العلامة المائية مع الآيات
                        # حساب المساحة التي تشغلها الآية
                        ayah_bottom = ayah_y + main_h

                        # إذا كانت العلامة المائية ستتداخل مع الآية، نضعها أسفل الآية مباشرة
                        if watermark_y < (ayah_bottom + 20):  # إضافة هامش 20 بكسل
                            watermark_y = min(ayah_bottom + 30, VIDEO_HEIGHT - 50)  # ضمان عدم الخروج عن حدود الفيديو

                        watermark = watermark_clip.set_position(('center', watermark_y)).set_duration(duration)

                        # إضافة العلامة المائية إلى قائمة المقاطع
                        clips.append(watermark)
                        print(f"تمت إضافة العلامة المائية '{WATERMARK_TEXT}' بنجاح")
                    except Exception as e:
                        print(f"خطأ في إنشاء العلامة المائية: {e}")

                # إنشاء الفيديو النهائي مع النص والعلامة المائية (إذا كانت مفعلة)
                final = CompositeVideoClip(clips)
                return final
            except Exception as e:
                print(f"[TextClip ARABIC GOLD error] {e}")
                return background

        elif english:
            ENGLISH_FONT_SIZE = 60
            try:
                # إضافة مسافة إضافية من الحواف (10 بيكسل)
                text_padding = 10

                # حساب العرض المتاح للنص مع مراعاة المسافة الإضافية
                available_width = int(VIDEO_WIDTH * 0.98) - (text_padding * 2)

                # إنشاء النص الإنجليزي مع مراعاة المسافات
                english_text = TextClip(
                    english,
                    font="Arial",
                    fontsize=ENGLISH_FONT_SIZE,
                    color='white',
                    method='caption',
                    size=(available_width, None),  # السماح بارتفاع تلقائي مع مراعاة المسافة
                    align='center'
                )

                # حساب ارتفاع النص
                text_h = english_text.size[1] if hasattr(english_text, 'size') else int(VIDEO_HEIGHT * 0.5)

                # توسيط النص في منتصف الشاشة مع إضافة مسافة إضافية من الأعلى والأسفل
                text_y = int((VIDEO_HEIGHT - text_h) / 2) + 10  # إضافة 10 بيكسل للمسافة العمودية

                # تعيين موضع النص
                english_text = english_text.set_position(('center', text_y))

                # تعيين مدة العرض
                english_text = english_text.set_duration(duration)
            except Exception as e:
                print(f"[TextClip ENGLISH Arial error] {e}")
                return background
            return CompositeVideoClip([background, english_text])

        return background

    except Exception as e:
        print(f"خطأ في إنشاء النص: {e}")
        print(f"تفاصيل الخطأ الكامل:", str(e))
        empty = ColorClip((VIDEO_WIDTH, VIDEO_HEIGHT), color=[0, 0, 0]).set_duration(duration)
        return empty

# دالة للكشف عن نوع كارت الشاشة (NVIDIA/AMD/Intel) والتحقق من توافق التعريفات
def detect_gpu_type():
    """
    تكتشف نوع كارت الشاشة المتوفر في النظام والتحقق من توافق التعريفات
    تعيد واحدة من القيم التالية: 'nvidia', 'amd', 'intel', 'unknown'
    """
    try:
        import subprocess
        import re

        # استخدام wmic للحصول على معلومات كارت الشاشة
        result = subprocess.run(
            ['wmic', 'path', 'win32_VideoController', 'get', 'name'],
            capture_output=True, text=True, check=True
        )

        gpu_info = result.stdout.lower()
        print(f"معلومات كارت الشاشة المكتشفة: {gpu_info}")

        # تحديد نوع كارت الشاشة - تحسين كشف NVIDIA
        if any(keyword in gpu_info for keyword in ['nvidia', 'geforce', 'rtx', 'gtx', 'quadro', 'tesla']):
            print("تم اكتشاف كارت شاشة NVIDIA")

            # التحقق من توافق تعريف NVIDIA مع NVENC - تحسين الفحص
            try:
                print("جاري التحقق من دعم NVENC...")

                # أولاً: فحص دعم FFmpeg لـ NVENC
                nvenc_check = subprocess.run(
                    [ffmpeg_path, '-hide_banner', '-encoders'],
                    capture_output=True, text=True, check=True
                )

                if 'h264_nvenc' in nvenc_check.stdout:
                    print("✅ FFmpeg يدعم h264_nvenc")

                    # ثانياً: اختبار فعلي لـ NVENC
                    print("جاري اختبار NVENC...")
                    test_cmd = [
                        ffmpeg_path, '-hide_banner', '-f', 'lavfi', '-i', 'testsrc=duration=1:size=320x240:rate=1',
                        '-c:v', 'h264_nvenc', '-preset', 'fast', '-f', 'null', '-'
                    ]

                    test_result = subprocess.run(
                        test_cmd, capture_output=True, text=True, timeout=10
                    )

                    if test_result.returncode == 0:
                        print("✅ تم التحقق من عمل NVENC بنجاح!")
                        print("🚀 سيتم استخدام تسريع NVIDIA NVENC")
                        return 'nvidia'
                    else:
                        print("❌ فشل اختبار NVENC:")
                        print(f"رسالة الخطأ: {test_result.stderr}")
                        print("💡 قد تحتاج إلى تحديث تعريف كارت الشاشة")
                        print("📝 سيتم استخدام الترميز العادي (libx264)")
                        return 'unknown'
                else:
                    print("❌ FFmpeg لا يدعم h264_nvenc")
                    print("💡 تأكد من أن FFmpeg مبني مع دعم NVENC")
                    print("📝 سيتم استخدام الترميز العادي (libx264)")
                    return 'unknown'

            except subprocess.TimeoutExpired:
                print("⏰ انتهت مهلة اختبار NVENC")
                print("📝 سيتم استخدام الترميز العادي (libx264)")
                return 'unknown'
            except Exception as e:
                print(f"❌ خطأ في التحقق من دعم NVENC: {e}")
                print("📝 سيتم استخدام الترميز العادي (libx264)")
                return 'unknown'

        elif 'amd' in gpu_info or 'radeon' in gpu_info:
            print("تم اكتشاف كارت شاشة AMD")

            # التحقق من توافق تعريف AMD مع AMF
            try:
                # محاولة تشغيل ffmpeg للتحقق من دعم AMF
                amf_check = subprocess.run(
                    ['ffmpeg', '-hide_banner', '-encoders'],
                    capture_output=True, text=True, check=True
                )

                # البحث عن h264_amf في قائمة الترميزات المدعومة
                if 'h264_amf' in amf_check.stdout:
                    print("تم التحقق من دعم AMF")
                    return 'amd'
                else:
                    print("كارت AMD موجود ولكن لا يدعم AMF أو التعريف قديم")
                    print("سيتم استخدام الترميز العادي (libx264)")
                    return 'unknown'
            except Exception as e:
                print(f"خطأ في التحقق من دعم AMF: {e}")
                print("سيتم استخدام الترميز العادي (libx264)")
                return 'unknown'

        elif 'intel' in gpu_info:
            print("تم اكتشاف كارت شاشة Intel")

            # التحقق من توافق تعريف Intel مع QSV
            try:
                # محاولة تشغيل ffmpeg للتحقق من دعم QSV
                qsv_check = subprocess.run(
                    ['ffmpeg', '-hide_banner', '-encoders'],
                    capture_output=True, text=True, check=True
                )

                # البحث عن h264_qsv في قائمة الترميزات المدعومة
                if 'h264_qsv' in qsv_check.stdout:
                    print("تم التحقق من دعم QSV")
                    return 'intel'
                else:
                    print("كارت Intel موجود ولكن لا يدعم QSV أو التعريف قديم")
                    print("سيتم استخدام الترميز العادي (libx264)")
                    return 'unknown'
            except Exception as e:
                print(f"خطأ في التحقق من دعم QSV: {e}")
                print("سيتم استخدام الترميز العادي (libx264)")
                return 'unknown'
        else:
            print("لم يتم التعرف على نوع كارت الشاشة، سيتم استخدام الإعدادات الافتراضية")
            return 'unknown'
    except Exception as e:
        print(f"خطأ في الكشف عن كارت الشاشة: {e}")
        return 'unknown'

# الكشف عن كارت الشاشة
# تفعيل استخدام GPU بعد تحديث التعريفات
GPU_TYPE = detect_gpu_type()  # تفعيل GPU مع أحدث التعريفات
# GPU_TYPE = 'unknown'  # يمكن تعطيل GPU بإلغاء التعليق على هذا السطر

# تحديد عدد النوى المتاحة للمعالجة
def get_optimal_threads():
    """تحديد العدد الأمثل للخيوط بناءً على عدد النوى المتاحة"""
    try:
        import multiprocessing
        cores = multiprocessing.cpu_count()
        # استخدام نصف عدد النوى المتاحة للحفاظ على استجابة النظام
        return max(1, cores // 2)
    except:
        # إذا فشل تحديد عدد النوى، استخدم 2 كقيمة افتراضية
        return 2

# تحديد عدد الخيوط المثالي
OPTIMAL_THREADS = get_optimal_threads()

# ثوابت لتحسين الأداء وجودة الفيديو - تحسين الجودة
VIDEO_ENCODING_SETTINGS = {
    'threads': OPTIMAL_THREADS,  # استخدام عدد الخيوط الأمثل
    'fps': 30,                   # معدل إطارات عالي لسلاسة أفضل
    'preset': 'slow',            # إعداد بطيء للحصول على أفضل جودة
    'codec': 'libx264',
    'audio_codec': 'aac',
    'audio_bitrate': '192k',     # جودة صوت أعلى
    'bitrate': '5000k',          # معدل بت أعلى للجودة العالية
    'write_logfile': False,
    'temp_audiofile': False,
    'remove_temp': True,
    'ffmpeg_params': [
        '-movflags', 'faststart',
        '-profile:v', 'high',    # بروفايل عالي للجودة الأفضل
        '-level', '4.1',         # مستوى أعلى يدعم دقة أعلى
        '-pix_fmt', 'yuv420p',
        '-crf', '18',            # جودة عالية جداً (18 بدلاً من 23)
        '-x264-params', 'ref=6:bframes=6:me=umh:subme=8:trellis=2:aq-mode=2'  # إعدادات x264 متقدمة
    ]
}

# إعدادات ترميز سريعة للفيديو (تحسين للأداء وتقليل استهلاك الذاكرة)
FAST_VIDEO_ENCODING_SETTINGS = {
    'threads': OPTIMAL_THREADS,   # استخدام عدد الخيوط الأمثل
    'fps': 30,                    # معدل إطارات جيد
    'preset': 'ultrafast',        # أسرع إعداد ممكن
    'codec': 'libx264',
    'audio_codec': 'aac',
    'audio_bitrate': '128k',      # جودة صوت جيدة
    'bitrate': '1500k',           # معدل بت أقل للسرعة
    'write_logfile': False,
    'temp_audiofile': False,
    'remove_temp': True,
    'ffmpeg_params': [
        '-movflags', 'faststart',
        '-profile:v', 'main',     # بروفايل متوسط
        '-level', '4.0',          # مستوى جيد
        '-pix_fmt', 'yuv420p',
        '-crf', '28',             # جودة أقل للسرعة
        '-bufsize', '1500k',      # مخزن مؤقت أصغر
        '-maxrate', '2000k',      # حد أقصى أقل
        '-x264-params', 'ref=1:bframes=0:me=dia:subme=1:trellis=0:aq-mode=0'  # أسرع إعدادات ممكنة
    ]
}

# إضافة خيارات تسريع الأجهزة حسب نوع كارت الشاشة
if GPU_TYPE == 'nvidia':
    # إعدادات NVIDIA NVENC للترميز السريع
    FAST_VIDEO_ENCODING_SETTINGS['codec'] = 'h264_nvenc'
    # حذف preset=ultrafast لأنه غير متوافق مع h264_nvenc
    FAST_VIDEO_ENCODING_SETTINGS.pop('preset', None)
    # حذف tune لأنه قد يكون غير متوافق
    if 'tune' in FAST_VIDEO_ENCODING_SETTINGS:
        FAST_VIDEO_ENCODING_SETTINGS.pop('tune', None)
    # إزالة tune من ffmpeg_params إذا كان موجوداً
    new_params = []
    skip_next = False
    for i, param in enumerate(FAST_VIDEO_ENCODING_SETTINGS['ffmpeg_params']):
        if skip_next:
            skip_next = False
            continue
        if param == '-tune':
            skip_next = True
            continue
        new_params.append(param)
    FAST_VIDEO_ENCODING_SETTINGS['ffmpeg_params'] = new_params

    # إضافة الإعدادات المتوافقة - محسنة للأداء العالي
    FAST_VIDEO_ENCODING_SETTINGS['ffmpeg_params'].extend([
        '-preset', 'p1',          # أسرع preset لـ NVENC
        '-rc', 'vbr',             # وضع معدل البت المتغير
        '-cq', '23',              # جودة أفضل
        '-b:v', '4000k',          # معدل بت أعلى
        '-maxrate', '6000k',      # حد أقصى أعلى
        '-bufsize', '8000k',      # مخزن مؤقت أكبر
        '-spatial-aq', '1',       # تحسين جودة المناطق المعقدة
        '-temporal-aq', '1',      # تحسين الجودة عبر الإطارات
        '-multipass', 'fullres',  # تمرير متعدد للجودة
        '-gpu', '0'               # استخدام أول GPU متاح
    ])

    # إعدادات NVIDIA NVENC للترميز عالي الجودة
    VIDEO_ENCODING_SETTINGS['codec'] = 'h264_nvenc'
    # حذف preset=medium لأنه غير متوافق مع h264_nvenc
    VIDEO_ENCODING_SETTINGS.pop('preset', None)
    # حذف tune لأنه قد يكون غير متوافق
    if 'tune' in VIDEO_ENCODING_SETTINGS:
        VIDEO_ENCODING_SETTINGS.pop('tune', None)
    # إزالة tune من ffmpeg_params إذا كان موجوداً
    new_params = []
    skip_next = False
    for i, param in enumerate(VIDEO_ENCODING_SETTINGS['ffmpeg_params']):
        if skip_next:
            skip_next = False
            continue
        if param == '-tune':
            skip_next = True
            continue
        new_params.append(param)
    VIDEO_ENCODING_SETTINGS['ffmpeg_params'] = new_params

    # إضافة الإعدادات المتوافقة - محسنة للجودة العالية والأداء
    VIDEO_ENCODING_SETTINGS['ffmpeg_params'].extend([
        '-preset', 'p4',          # أفضل جودة لـ NVENC
        '-rc', 'vbr',             # وضع معدل البت المتغير
        '-cq', '18',              # جودة عالية جداً
        '-b:v', '8000k',          # معدل بت عالي جداً
        '-maxrate', '12000k',     # حد أقصى عالي
        '-bufsize', '16000k',     # مخزن مؤقت كبير
        '-spatial-aq', '1',       # تحسين جودة المناطق المعقدة
        '-temporal-aq', '1',      # تحسين الجودة عبر الإطارات
        '-multipass', 'fullres',  # تمرير متعدد للجودة القصوى
        '-lookahead', '32',       # نظرة مستقبلية للتحسين
        '-gpu', '0'               # استخدام أول GPU متاح
    ])
    print("تم تفعيل تسريع NVIDIA NVENC")

elif GPU_TYPE == 'amd':
    # إعدادات AMD AMF للترميز السريع
    FAST_VIDEO_ENCODING_SETTINGS['codec'] = 'h264_amf'
    # حذف preset=ultrafast لأنه غير متوافق مع h264_amf
    FAST_VIDEO_ENCODING_SETTINGS.pop('preset', None)
    # حذف tune لأنه قد يكون غير متوافق
    if 'tune' in FAST_VIDEO_ENCODING_SETTINGS:
        FAST_VIDEO_ENCODING_SETTINGS.pop('tune', None)
    # إزالة tune من ffmpeg_params إذا كان موجوداً
    new_params = []
    skip_next = False
    for i, param in enumerate(FAST_VIDEO_ENCODING_SETTINGS['ffmpeg_params']):
        if skip_next:
            skip_next = False
            continue
        if param == '-tune':
            skip_next = True
            continue
        new_params.append(param)
    FAST_VIDEO_ENCODING_SETTINGS['ffmpeg_params'] = new_params

    # إضافة الإعدادات المتوافقة
    FAST_VIDEO_ENCODING_SETTINGS['ffmpeg_params'].extend([
        '-quality', 'speed',      # تحسين للسرعة
        '-usage', 'ultralowlatency', # أقل تأخير ممكن
        '-rc', 'vbr_peak_constrained' # معدل بت متغير مع تحديد الذروة
    ])

    # إعدادات AMD AMF للترميز عالي الجودة
    VIDEO_ENCODING_SETTINGS['codec'] = 'h264_amf'
    # حذف preset=medium لأنه غير متوافق مع h264_amf
    VIDEO_ENCODING_SETTINGS.pop('preset', None)
    # حذف tune لأنه قد يكون غير متوافق
    if 'tune' in VIDEO_ENCODING_SETTINGS:
        VIDEO_ENCODING_SETTINGS.pop('tune', None)
    # إزالة tune من ffmpeg_params إذا كان موجوداً
    new_params = []
    skip_next = False
    for i, param in enumerate(VIDEO_ENCODING_SETTINGS['ffmpeg_params']):
        if skip_next:
            skip_next = False
            continue
        if param == '-tune':
            skip_next = True
            continue
        new_params.append(param)
    VIDEO_ENCODING_SETTINGS['ffmpeg_params'] = new_params

    # إضافة الإعدادات المتوافقة
    VIDEO_ENCODING_SETTINGS['ffmpeg_params'].extend([
        '-quality', 'balanced',   # توازن بين الجودة والسرعة
        '-usage', 'transcoding',  # مناسب للتحويل
        '-rc', 'vbr_latency'      # معدل بت متغير مع تأخير منخفض
    ])
    print("تم تفعيل تسريع AMD AMF")

elif GPU_TYPE == 'intel':
    # إعدادات Intel QSV للترميز السريع
    FAST_VIDEO_ENCODING_SETTINGS['codec'] = 'h264_qsv'
    # حذف preset=ultrafast لأنه غير متوافق مع h264_qsv
    FAST_VIDEO_ENCODING_SETTINGS.pop('preset', None)
    # حذف tune لأنه قد يكون غير متوافق
    if 'tune' in FAST_VIDEO_ENCODING_SETTINGS:
        FAST_VIDEO_ENCODING_SETTINGS.pop('tune', None)
    # إزالة tune من ffmpeg_params إذا كان موجوداً
    new_params = []
    skip_next = False
    for i, param in enumerate(FAST_VIDEO_ENCODING_SETTINGS['ffmpeg_params']):
        if skip_next:
            skip_next = False
            continue
        if param == '-tune':
            skip_next = True
            continue
        new_params.append(param)
    FAST_VIDEO_ENCODING_SETTINGS['ffmpeg_params'] = new_params

    # إضافة الإعدادات المتوافقة
    FAST_VIDEO_ENCODING_SETTINGS['ffmpeg_params'].extend([
        '-global_quality', '28',  # جودة منخفضة للسرعة
        '-look_ahead', '0'        # تعطيل النظر للأمام لزيادة السرعة
    ])

    # إعدادات Intel QSV للترميز عالي الجودة
    VIDEO_ENCODING_SETTINGS['codec'] = 'h264_qsv'
    # حذف preset=medium لأنه غير متوافق مع h264_qsv
    VIDEO_ENCODING_SETTINGS.pop('preset', None)
    # حذف tune لأنه قد يكون غير متوافق
    if 'tune' in VIDEO_ENCODING_SETTINGS:
        VIDEO_ENCODING_SETTINGS.pop('tune', None)
    # إزالة tune من ffmpeg_params إذا كان موجوداً
    new_params = []
    skip_next = False
    for i, param in enumerate(VIDEO_ENCODING_SETTINGS['ffmpeg_params']):
        if skip_next:
            skip_next = False
            continue
        if param == '-tune':
            skip_next = True
            continue
        new_params.append(param)
    VIDEO_ENCODING_SETTINGS['ffmpeg_params'] = new_params

    # إضافة الإعدادات المتوافقة
    VIDEO_ENCODING_SETTINGS['ffmpeg_params'].extend([
        '-global_quality', '23',  # جودة أعلى
        '-look_ahead', '1'        # تفعيل النظر للأمام لتحسين الجودة
    ])
    print("تم تفعيل تسريع Intel QSV")

# متغير للتحكم في استخدام الإعدادات السريعة (مفعل افتراضياً لتحسين الأداء)
USE_FAST_ENCODING = True

# دالة لحفظ إعدادات المستخدم
def save_user_settings():
    """حفظ إعدادات المستخدم في ملف"""
    try:
        settings = {
            "output_resolution": OUTPUT_RESOLUTION,
            "use_fast_encoding": USE_FAST_ENCODING,
            "video_aspect_ratio": VIDEO_ASPECT_RATIO,
            "arabic_font_size_factor": ARABIC_FONT_SIZE_FACTOR,
            "arabic_font_color": ARABIC_FONT_COLOR,
            "show_watermark": SHOW_WATERMARK,
            "watermark_text": WATERMARK_TEXT,
            "watermark_opacity": WATERMARK_OPACITY,
            "watermark_size_factor": WATERMARK_SIZE_FACTOR
        }

        settings_path = os.path.join(EXEC_DIR, "user_settings.json")
        with open(settings_path, "w", encoding="utf-8") as f:
            json.dump(settings, f, ensure_ascii=False, indent=4)
        print("تم حفظ إعدادات المستخدم")
    except Exception as e:
        print(f"خطأ في حفظ إعدادات المستخدم: {e}")

# دالة لتحميل إعدادات المستخدم
def load_user_settings():
    """تحميل إعدادات المستخدم من ملف"""
    try:
        global OUTPUT_RESOLUTION, USE_FAST_ENCODING, VIDEO_ASPECT_RATIO
        global ARABIC_FONT_SIZE_FACTOR, ARABIC_FONT_COLOR, SHOW_WATERMARK
        global WATERMARK_TEXT, WATERMARK_OPACITY, WATERMARK_SIZE_FACTOR

        settings_path = os.path.join(EXEC_DIR, "user_settings.json")
        if os.path.exists(settings_path):
            with open(settings_path, "r", encoding="utf-8") as f:
                settings = json.load(f)

            OUTPUT_RESOLUTION = settings.get("output_resolution", OUTPUT_RESOLUTION)
            USE_FAST_ENCODING = settings.get("use_fast_encoding", USE_FAST_ENCODING)
            VIDEO_ASPECT_RATIO = settings.get("video_aspect_ratio", VIDEO_ASPECT_RATIO)
            ARABIC_FONT_SIZE_FACTOR = settings.get("arabic_font_size_factor", ARABIC_FONT_SIZE_FACTOR)
            ARABIC_FONT_COLOR = settings.get("arabic_font_color", ARABIC_FONT_COLOR)
            SHOW_WATERMARK = settings.get("show_watermark", SHOW_WATERMARK)
            WATERMARK_TEXT = settings.get("watermark_text", WATERMARK_TEXT)
            WATERMARK_OPACITY = settings.get("watermark_opacity", WATERMARK_OPACITY)
            WATERMARK_SIZE_FACTOR = settings.get("watermark_size_factor", WATERMARK_SIZE_FACTOR)

            print("تم تحميل إعدادات المستخدم")
    except Exception as e:
        print(f"خطأ في تحميل إعدادات المستخدم: {e}")

# تحميل الإعدادات عند بدء البرنامج
load_user_settings()

# دالة للتحقق من توافق GPU والتراجع إلى الترميز العادي عند الحاجة
def fallback_to_cpu_encoding():
    """التراجع إلى الترميز العادي في حالة فشل GPU"""
    global VIDEO_ENCODING_SETTINGS, FAST_VIDEO_ENCODING_SETTINGS, GPU_TYPE

    print("تم اكتشاف مشكلة في تسريع GPU، سيتم التراجع إلى الترميز العادي...")

    # إعادة تعيين إعدادات الترميز إلى الوضع العادي
    VIDEO_ENCODING_SETTINGS = {
        'threads': OPTIMAL_THREADS,
        'fps': 30,
        'preset': 'slow',
        'codec': 'libx264',
        'audio_codec': 'aac',
        'audio_bitrate': '192k',
        'bitrate': '5000k',
        'write_logfile': False,
        'temp_audiofile': False,
        'remove_temp': True,
        'ffmpeg_params': [
            '-movflags', 'faststart',
            '-profile:v', 'high',
            '-level', '4.1',
            '-pix_fmt', 'yuv420p',
            '-crf', '18',
            '-x264-params', 'ref=6:bframes=6:me=umh:subme=8:trellis=2:aq-mode=2'
        ]
    }

    FAST_VIDEO_ENCODING_SETTINGS = {
        'threads': OPTIMAL_THREADS,
        'fps': 30,
        'preset': 'veryfast',
        'codec': 'libx264',
        'audio_codec': 'aac',
        'audio_bitrate': '128k',
        'bitrate': '2500k',
        'write_logfile': False,
        'temp_audiofile': False,
        'remove_temp': True,
        'ffmpeg_params': [
            '-movflags', 'faststart',
            '-profile:v', 'main',
            '-level', '4.0',
            '-pix_fmt', 'yuv420p',
            '-crf', '23',
            '-bufsize', '2500k',
            '-maxrate', '3000k',
            '-x264-params', 'ref=3:bframes=3:me=hex:subme=6'
        ]
    }

    # تحديث نوع GPU
    GPU_TYPE = 'unknown'
    print("تم التراجع إلى الترميز العادي بنجاح")

def update_progress(progress_text, value=None):
    """تحديث شريط التقدم وعرض الحالة"""
    # استخدام status_label بدلاً من progress_label
    status_label.config(text=progress_text)
    if value is not None:
        progress_bar["value"] = value
    root.update_idletasks()

# 🚀 دالة محسنة جذرياً لإنشاء الفيديو - حل جذري للمشاكل الحرجة
def build_video_ultra_fast(reciter_display, surah_name, start_ayah, end_ayah, translation_display, translation_file=None):
    """
    🔥 حل جذري للمشاكل الحرجة:
    1. فيديو واحد موحد بدلاً من مقاطع منفصلة لكل آية
    2. تحميل فيديو الخلفية مرة واحدة فقط
    3. ذاكرة مؤقتة ذكية للنصوص
    4. دمج فعال بدون concatenation
    """
    try:
        # التحقق من حالة الإيقاف
        global STOP_ALL_OPERATIONS, STOP_REQUESTED, BACKGROUND_VIDEO_PATH
        if STOP_REQUESTED or STOP_ALL_OPERATIONS:
            print("تم إلغاء إنشاء الفيديو بسبب طلب الإيقاف")
            return

        print("🚀 بدء إنشاء الفيديو بالطريقة المحسنة الجذرية...")
        clear_outputs()

        # التحقق من صحة المدخلات
        surah = SURAH_NAMES.index(surah_name) + 1
        reciter_id = RECITERS_MAP[reciter_display]
        start_ayah, end_ayah = int(start_ayah), int(end_ayah)
        total_ayats = end_ayah - start_ayah + 1

        # تحديد أبعاد الفيديو
        global VIDEO_WIDTH, VIDEO_HEIGHT
        if OUTPUT_RESOLUTION == "16:9":
            VIDEO_WIDTH, VIDEO_HEIGHT = VIDEO_RESOLUTIONS["16:9"][resolution_var.get()]
        else:
            VIDEO_WIDTH, VIDEO_HEIGHT = VIDEO_RESOLUTIONS["9:16"][resolution_var.get()]

        progress_bar["maximum"] = 100

        # الخطوة 1: تحميل جميع الملفات الصوتية بشكل متوازي
        update_progress("🔄 تحميل الملفات الصوتية بسرعة فائقة...", 10)
        audio_files = []
        audio_durations = []

        with ThreadPoolExecutor(max_workers=12) as executor:  # زيادة عدد الخيوط
            audio_futures = []
            for idx, ayah in enumerate(range(start_ayah, end_ayah + 1)):
                future = executor.submit(download_audio, reciter_id, surah, ayah, idx)
                audio_futures.append((future, ayah))

            for future, ayah in audio_futures:
                try:
                    audio_path = future.result()
                    # استخدام pydub للحصول على المدة بسرعة أكبر
                    from pydub import AudioSegment
                    audio_segment = AudioSegment.from_file(audio_path)
                    duration = len(audio_segment) / 1000.0

                    audio_files.append(audio_path)
                    audio_durations.append(duration)
                except Exception as e:
                    print(f"خطأ في تحميل صوت الآية {ayah}: {e}")
                    # إنشاء صوت صامت بديل
                    silence_path = os.path.join(AUDIO_DIR, f"silence_{ayah}.mp3")
                    from pydub import AudioSegment
                    silence = AudioSegment.silent(duration=3000)  # 3 ثوان
                    silence.export(silence_path, format="mp3")
                    audio_files.append(silence_path)
                    audio_durations.append(3.0)

        # الخطوة 2: دمج جميع الملفات الصوتية في ملف واحد مع فترات صمت
        update_progress("🎵 دمج الملفات الصوتية مع انتقالات سلسة...", 30)
        combined_audio_path = os.path.join(AUDIO_DIR, "combined_audio_optimized.mp3")

        from pydub import AudioSegment
        combined_audio = AudioSegment.empty()

        for i, audio_path in enumerate(audio_files):
            try:
                audio_segment = AudioSegment.from_file(audio_path)
                combined_audio += audio_segment

                # إضافة فترة صمت قصيرة بين الآيات (ما عدا الأخيرة)
                if i < len(audio_files) - 1:
                    silence = AudioSegment.silent(duration=300)  # 0.3 ثانية
                    combined_audio += silence

            except Exception as e:
                print(f"خطأ في دمج الصوت {i}: {e}")

        # تصدير الصوت المدمج بجودة محسنة
        combined_audio.export(combined_audio_path, format="mp3", bitrate="128k")
        total_duration = len(combined_audio) / 1000.0  # تحويل إلى ثوان

        # الخطوة 3: تحضير النصوص بسرعة
        update_progress("📝 تحضير النصوص بسرعة فائقة...", 50)
        all_texts = []

        with ThreadPoolExecutor(max_workers=12) as executor:
            text_futures = []
            for ayah in range(start_ayah, end_ayah + 1):
                if translation_file:
                    # استخدام ملف الترجمة المحدد
                    future = executor.submit(lambda a: f"آية {a}", ayah)  # مؤقت
                else:
                    future = executor.submit(get_ayah_text, surah, ayah)
                text_futures.append((future, ayah))

            for future, ayah in text_futures:
                try:
                    text = future.result()
                    all_texts.append(text)
                except Exception as e:
                    print(f"خطأ في تحميل نص الآية {ayah}: {e}")
                    all_texts.append(f"آية {ayah}")

        # الخطوة 4: إنشاء فيديو واحد بدلاً من مقاطع منفصلة
        update_progress("🎬 إنشاء الفيديو الموحد بسرعة البرق...", 70)

        # تحميل فيديو الخلفية مرة واحدة فقط
        try:
            background_video = VideoFileClip(BACKGROUND_VIDEO_PATH, audio=False)

            # تحسين: تكرار الفيديو بكفاءة
            if background_video.duration < total_duration:
                loops_needed = int(total_duration / background_video.duration) + 1
                background_video = background_video.loop(n=loops_needed)

            # قص الفيديو للمدة المطلوبة
            background_video = background_video.subclip(0, total_duration)

            # تغيير حجم الفيديو مرة واحدة بكفاءة
            if background_video.size != (VIDEO_WIDTH, VIDEO_HEIGHT):
                background_video = background_video.resize((VIDEO_WIDTH, VIDEO_HEIGHT))

        except Exception as e:
            print(f"خطأ في تحميل فيديو الخلفية: {e}")
            # إنشاء فيديو خلفية بسيط
            from moviepy.editor import ColorClip
            background_video = ColorClip(size=(VIDEO_WIDTH, VIDEO_HEIGHT),
                                       color=(20, 30, 40), duration=total_duration)

        # الخطوة 5: إنشاء النصوص المتحركة بكفاءة عالية
        update_progress("✍️ إنشاء النصوص المتحركة بتقنية محسنة...", 85)

        text_clips = []
        current_time = 0

        for i, (text, duration) in enumerate(zip(all_texts, audio_durations)):
            try:
                # إنشاء نص محسن للسرعة
                txt_clip = TextClip(text,
                                  fontsize=50,  # حجم أصغر للسرعة
                                  color='white',
                                  font='Arial',  # خط أبسط للسرعة
                                  size=(VIDEO_WIDTH-100, None),
                                  method='caption')

                # تحديد موقع ووقت النص
                txt_clip = txt_clip.set_position('center').set_start(current_time).set_duration(duration)
                text_clips.append(txt_clip)

                current_time += duration + 0.3  # إضافة فترة الصمت

            except Exception as e:
                print(f"خطأ في إنشاء نص الآية {i+1}: {e}")

        # الخطوة 6: دمج كل شيء بكفاءة قصوى
        update_progress("🔗 دمج الفيديو النهائي بسرعة فائقة...", 95)

        # دمج الفيديو مع النصوص
        if text_clips:
            final_video = CompositeVideoClip([background_video] + text_clips)
        else:
            final_video = background_video

        # إضافة الصوت المدمج
        combined_audio_clip = AudioFileClip(combined_audio_path)
        final_video = final_video.set_audio(combined_audio_clip)

        # الخطوة 7: حفظ الفيديو بأقصى سرعة
        update_progress("💾 حفظ الفيديو بسرعة البرق...", 98)

        # تحديد اسم الملف
        resolution_text = "HD" if OUTPUT_RESOLUTION == "16:9" else "Mobile"
        if translation_file:
            filename = f"{surah_name}_الآيات_{start_ayah}-{end_ayah}_{resolution_text}_ترجمة_محسن.mp4"
        else:
            filename = f"{surah_name}_الآيات_{start_ayah}-{end_ayah}_{resolution_text}_محسن.mp4"

        out_path = os.path.join(VIDEO_DIR, filename)

        # استخدام إعدادات الترميز فائقة السرعة
        ultra_fast_settings = FAST_VIDEO_ENCODING_SETTINGS.copy()
        ultra_fast_settings['preset'] = 'ultrafast'  # أسرع إعداد ممكن
        ultra_fast_settings['threads'] = 8  # المزيد من الخيوط

        try:
            final_video.write_videofile(out_path, **ultra_fast_settings, verbose=False, logger=None)
        except TypeError:
            final_video.write_videofile(out_path, **ultra_fast_settings, verbose=False)

        # تنظيف الذاكرة
        background_video.close()
        combined_audio_clip.close()
        final_video.close()
        for clip in text_clips:
            clip.close()

        # حذف الملفات المؤقتة
        try:
            os.remove(combined_audio_path)
        except:
            pass

        update_progress("✅ تم إنشاء الفيديو بنجاح بسرعة فائقة!", 100)
        print(f"🎉 تم إنشاء الفيديو المحسن: {out_path}")

        # عرض رسالة نجاح
        messagebox.showinfo("نجح", f"تم إنشاء الفيديو بنجاح بسرعة فائقة!\n{filename}")

    except Exception as e:
        print(f"❌ خطأ في إنشاء الفيديو المحسن: {e}")
        messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء الفيديو:\n{str(e)}")

# تعديل دالة build_video لاستخدام الطريقة المحسنة
def build_video(reciter_display, surah_name, start_ayah, end_ayah, translation_display, translation_file=None):
    """استخدام الطريقة المحسنة لإنشاء الفيديو بسرعة فائقة"""
    return build_video_ultra_fast(reciter_display, surah_name, start_ayah, end_ayah, translation_display, translation_file)
        # التحقق من حالة الإيقاف
        global STOP_ALL_OPERATIONS, STOP_REQUESTED, BACKGROUND_VIDEO_PATH
        if STOP_REQUESTED or STOP_ALL_OPERATIONS:
            print("تم إلغاء إنشاء الفيديو بسبب طلب الإيقاف")
            return

        # طباعة معلومات الخلفية في بداية الدالة
        print(f"معلومات الخلفية: BACKGROUND_VIDEO_PATH = {BACKGROUND_VIDEO_PATH}")

        # التحقق من وجود ملفات قيد الاستخدام قبل تنظيف المجلدات
        files_in_use = []
        for d in (AUDIO_DIR, VIDEO_DIR):
            if os.path.isdir(d):
                for root_dir, _, files in os.walk(d):
                    for file in files:
                        file_path = os.path.join(root_dir, file)
                        try:
                            # محاولة فتح الملف للكتابة للتحقق مما إذا كان قيد الاستخدام
                            with open(file_path, 'a'):
                                pass
                        except:
                            # إذا فشل فتح الملف، فهو قيد الاستخدام
                            files_in_use.append(file_path)

        if files_in_use:
            print(f">>> تحذير: هناك {len(files_in_use)} ملفات قيد الاستخدام. سيتم تجاهلها أثناء التنظيف.")
            for file in files_in_use[:5]:  # عرض أول 5 ملفات فقط لتجنب الإطالة
                print(f">>> ملف قيد الاستخدام: {file}")
            if len(files_in_use) > 5:
                print(f">>> ... و{len(files_in_use) - 5} ملفات أخرى")

        print(">>> [1] تنظيف المجلدات...", flush=True)
        clear_outputs()
        update_progress("جاري تنظيف المجلدات...")

        # التحقق من صحة المدخلات
        surah = SURAH_NAMES.index(surah_name) + 1
        reciter_id = RECITERS_MAP[reciter_display]

        try:
            start_ayah = int(start_ayah)
            end_ayah = int(end_ayah)
        except ValueError:
            messagebox.showerror("خطأ", "يجب أن تكون أرقام الآيات أعداداً صحيحة")
            return

        max_ayah = VERSE_COUNTS.get(surah, 0)
        if start_ayah < 1 or end_ayah > max_ayah or start_ayah > end_ayah:
            messagebox.showerror("خطأ", f"نطاق الآيات غير صحيح. يجب أن يكون بين 1 و {max_ayah}")
            return

        total_ayats = end_ayah - start_ayah + 1
        progress_bar["maximum"] = total_ayats * 2  # مضاعفة للتحميل والمعالجة

        # معالجة الآيات بشكل متوازٍ مع إدارة أفضل للذاكرة
        arabic_clips = []
        durations = []
        translations = {lang_id: [] for lang_id in DEFAULT_TRANSLATIONS.values()}

        # تحديد عدد العمليات المتوازية بناءً على وضع الترميز (تحسين الأداء)
        max_workers = 6 if USE_FAST_ENCODING else 4



        # استخدام ThreadPoolExecutor لتنزيل الملفات بشكل متوازي
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = []

            # تحميل البيانات
            for idx, ayah in enumerate(range(start_ayah, end_ayah + 1), 1):
                # التحقق من حالة الإيقاف
                if STOP_REQUESTED or STOP_ALL_OPERATIONS:
                    print("تم إلغاء إنشاء الفيديو بسبب طلب الإيقاف")
                    return

                update_progress(f"جاري تحميل الآية {ayah}...", idx)

                future_audio = executor.submit(download_audio, reciter_id, surah, ayah, idx - 1)
                future_arabic = executor.submit(get_ayah_text, surah, ayah)
                future_translations = {
                    lang_id: executor.submit(get_ayah_translation, surah, ayah, lang_id)
                    for lang_id in DEFAULT_TRANSLATIONS.values()
                }

                futures.append((future_audio, future_arabic, future_translations, ayah, idx))

            # معالجة النتائج
            for future_audio, future_arabic, future_translations, ayah, idx in futures:
                try:
                    # التحقق من حالة الإيقاف
                    if STOP_REQUESTED or STOP_ALL_OPERATIONS:
                        print("تم إلغاء إنشاء الفيديو بسبب طلب الإيقاف")
                        return

                    update_progress(f"جاري معالجة الآية {ayah}...", total_ayats + idx)

                    # محاولة الحصول على نتائج الخيوط مع التعامل مع الأخطاء
                    try:
                        ap = future_audio.result()
                        arabic_text = future_arabic.result()
                    except Exception as e:
                        print(f"احدث خطأ أثناء معالجة الآية {ayah}. سيتم تخطى هذه الآية")
                        print(f"التفاصيل: {str(e)}")
                        continue  # تخطي هذه الآية والانتقال إلى الآية التالية

                    # محاولة تحميل الملف الصوتي
                    try:
                        audio_clip = AudioFileClip(ap)
                        dur = audio_clip.duration
                    except Exception as e:
                        print(f"خطأ في تحميل الملف الصوتي للآية {ayah}: {str(e)}")
                        # استخدام مدة افتراضية في حالة الخطأ
                        dur = 5.0
                        # إنشاء ملف صوتي صامت
                        from moviepy.audio.AudioClip import AudioClip
                        audio_clip = AudioClip(lambda t: 0, duration=dur)

                    # إذا تم تحديد ملف ترجمة، استخدمه بدلاً من ترجمة الميسر
                    if translation_file:
                        try:
                            # قراءة ملف الترجمة المحدد
                            translation_path = os.path.join(EXEC_DIR, translation_file)
                            translation_texts = parse_srt_file(translation_path)

                            # التأكد من أن هناك ترجمات كافية
                            if idx-1 < len(translation_texts):
                                translation_text = translation_texts[idx-1]
                                print(f"تم استخدام الترجمة من الملف {translation_file} للآية {ayah}: {translation_text[:30]}...")

                                # استخدام الترجمة من الملف المحدد
                                arabic_clip = make_text_clip(translation_text, "", dur, ayah, BACKGROUND_VIDEO_PATH)
                            else:
                                # إذا لم تكن هناك ترجمات كافية، استخدم النص العربي الأصلي
                                print(f"لا توجد ترجمة كافية في الملف {translation_file} للآية {ayah}")
                                arabic_clip = make_text_clip(arabic_text, "", dur, ayah, BACKGROUND_VIDEO_PATH)
                        except Exception as e:
                            print(f"خطأ في استخدام ملف الترجمة: {e}")
                            # استخدام النص العربي الأصلي كبديل في حالة الخطأ
                            arabic_clip = make_text_clip(arabic_text, "", dur, ayah, BACKGROUND_VIDEO_PATH)
                    else:
                        # محاولة تحميل ترجمة الميسر العربية (السلوك الافتراضي)
                        try:
                            muyassar_translation = get_ayah_translation(surah, ayah, "ar.muyassar")
                            print(f"تم تحميل ترجمة الميسر للآية {ayah}: {muyassar_translation[:30]}...")

                            # استخدام ترجمة الميسر بدلاً من نص الآية
                            # طباعة مسار الخلفية للتأكد من أنه صحيح
                            print(f"مسار الخلفية: {BACKGROUND_VIDEO_PATH}")
                            arabic_clip = make_text_clip(muyassar_translation, "", dur, ayah, BACKGROUND_VIDEO_PATH)
                        except Exception as e:
                            print(f"خطأ في تحميل ترجمة الميسر: {e}")
                            # استخدام النص العربي الأصلي كبديل في حالة الخطأ
                            print(f"مسار الخلفية: {BACKGROUND_VIDEO_PATH}")
                            arabic_clip = make_text_clip(arabic_text, "", dur, ayah, BACKGROUND_VIDEO_PATH)

                    # إضافة الصوت إلى المقطع
                    try:
                        arabic_clip = arabic_clip.set_audio(audio_clip)
                    except Exception as e:
                        print(f"خطأ في إضافة الصوت إلى المقطع للآية {ayah}: {str(e)}")
                        # استمرار بدون صوت في حالة الخطأ

                    # إضافة المقطع إلى القائمة
                    arabic_clips.append(arabic_clip)
                    durations.append(dur)

                    # إذا لم يتم تحديد ملف ترجمة، قم بإنشاء ملفات الترجمة العادية
                    if not translation_file:
                        for lang_id, future_trans in future_translations.items():
                            try:
                                trans_text = future_trans.result()
                                translations[lang_id].append(trans_text)
                            except Exception as e:
                                print(f"خطأ في الحصول على ترجمة {lang_id} للآية {ayah}: {str(e)}")
                                # استخدام نص بديل في حالة الخطأ
                                translations[lang_id].append(f"Translation for verse {surah}:{ayah} not available")

                    # تحرير الذاكرة بعد كل مقطع بشكل أكثر فعالية
                    import gc
                    # تنظيف الذاكرة المؤقتة لـ MoviePy
                    try:
                        from moviepy.video.io.ffmpeg_reader import ffmpeg_parse_infos
                        if hasattr(ffmpeg_parse_infos, "cache"):
                            ffmpeg_parse_infos.cache.clear()
                    except:
                        pass
                    # إجبار نظام Python على تحرير الذاكرة
                    gc.collect(2)  # إجبار تنظيف الجيل الثاني من الكائنات

                    # تحديث الواجهة لمنع التجميد
                    root.update_idletasks()

                except Exception as e:
                    print(f"احدث خطأ أثناء معالجة الآية {ayah}. سيتم تخطى هذه الآية")
                    print(f"التفاصيل: {str(e)}")
                    # لا نعرض رسالة تحذير للمستخدم لتجنب تعطيل العملية
                    # بدلاً من ذلك، نطبع الخطأ في وحدة التحكم فقط

        if not arabic_clips:
            messagebox.showerror("خطأ", "لم يتم إنشاء أي مقاطع فيديو")
            return

        # التحقق من حالة الإيقاف
        if STOP_REQUESTED or STOP_ALL_OPERATIONS:
            print("تم إلغاء إنشاء الفيديو بسبب طلب الإيقاف")
            return

        # إنشاء الفيديو النهائي مع تحسينات لمنع التهنيج
        update_progress("جاري إنشاء الفيديو النهائي...")

        # تحديث الواجهة قبل العملية الثقيلة
        root.update_idletasks()

        # تحرير الذاكرة قبل دمج المقاطع
        import gc
        gc.collect(2)

        # تحسين دمج المقاطع لإزالة التقطيع والأصوات المزعجة
        update_progress("جاري دمج المقاطع مع انتقالات سلسة...")

        # إضافة فترة صمت قصيرة بين الآيات لتجنب التقطيع
        try:
            from moviepy.audio.AudioClip import AudioClip

            # إنشاء مقاطع محسنة مع فترات صمت
            enhanced_clips = []
            for i, clip in enumerate(arabic_clips):
                # إضافة المقطع الأصلي
                enhanced_clips.append(clip)

                # إضافة فترة صمت قصيرة بين الآيات (ما عدا الآية الأخيرة)
                if i < len(arabic_clips) - 1:
                    # إنشاء مقطع صمت قصير (0.3 ثانية)
                    silence_duration = 0.3

                    # إنشاء مقطع فيديو صامت بنفس خصائص المقطع السابق
                    silence_video = clip.subclip(0, silence_duration).without_audio()

                    # إنشاء صوت صامت
                    silence_audio = AudioClip(lambda t: 0, duration=silence_duration)

                    # دمج الفيديو الصامت مع الصوت الصامت
                    silence_clip = silence_video.set_audio(silence_audio)

                    enhanced_clips.append(silence_clip)

            # دمج المقاطع المحسنة
            final_video = concatenate_videoclips(enhanced_clips, method='compose')
            print("تم دمج المقاطع مع انتقالات سلسة")

        except Exception as e:
            print(f"خطأ في إنشاء الانتقالات السلسة: {e}")
            print("سيتم استخدام الطريقة العادية...")

            # استخدام الطريقة العادية في حالة الفشل
            try:
                final_video = concatenate_videoclips(arabic_clips, method='compose')
                print("تم دمج المقاطع بنجاح")
            except Exception as e:
                print(f"خطأ في دمج المقاطع: {e}")
                # محاولة استخدام طريقة أخرى في حالة الفشل
                try:
                    final_video = concatenate_videoclips(arabic_clips, method='chain')
                    print("تم دمج المقاطع باستخدام طريقة chain")
                except Exception as e2:
                    print(f"فشل في دمج المقاطع باستخدام طريقة chain: {e2}")
                    # محاولة أخيرة باستخدام طريقة أبسط
                    if len(arabic_clips) > 0:
                        final_video = arabic_clips[0]
                        for clip in arabic_clips[1:]:
                            final_video = concatenate_videoclips([final_video, clip])
                        print("تم دمج المقاطع باستخدام الطريقة البسيطة")
                    else:
                        raise Exception("لا توجد مقاطع للدمج")

        # تحديد اسم الملف بناءً على الدقة المختارة والترجمة المستخدمة
        resolution_text = "HD" if OUTPUT_RESOLUTION == "16:9" else "Mobile"

        # استخراج رمز اللغة من معرف الترجمة أو ملف الترجمة
        lang_code = "ar"  # افتراضي للعربية

        if translation_file:
            # استخراج رمز اللغة من اسم ملف الترجمة (مثل translation_en.sahih.srt)
            file_name = os.path.basename(translation_file)
            if file_name.startswith("translation_") and "_" in file_name:
                lang_parts = file_name.split("_")[1].split(".")
                if lang_parts and len(lang_parts[0]) <= 3:
                    lang_code = lang_parts[0]
        elif translation_display:
            # استخراج رمز اللغة من معرف الترجمة المختارة
            translation_id = TRANSLATIONS_MAP.get(translation_display, "")
            if "." in translation_id:
                lang_code = translation_id.split(".")[0]

        out_filename = f"final_video_{lang_code}_{resolution_text}.mp4"
        out_path = os.path.join(EXEC_DIR, out_filename)

        # الحصول على الأبعاد من القاموس حسب وضع الترميز
        if USE_FAST_ENCODING:
            # استخدام دقة أقل في الوضع السريع
            if OUTPUT_RESOLUTION in FAST_VIDEO_RESOLUTIONS:
                width, height = FAST_VIDEO_RESOLUTIONS[OUTPUT_RESOLUTION]
                # تغيير حجم الفيديو النهائي ليناسب الدقة المختارة
                if final_video.size != (width, height):
                    update_progress(f"جاري تغيير حجم الفيديو إلى {width}×{height} (وضع سريع)...")
                    # استخدام الطريقة العادية دائمًا لتجنب مشاكل التوافق مع GPU
                    try:
                        final_video = final_video.resize(width=width, height=height)
                        print("تم تغيير حجم الفيديو بنجاح")
                    except Exception as e:
                        print(f"خطأ في تغيير حجم الفيديو: {e}")
                        # استمرار بدون تغيير الحجم في حالة الخطأ
        else:
            # استخدام الدقة العادية
            if OUTPUT_RESOLUTION in VIDEO_RESOLUTIONS:
                width, height = VIDEO_RESOLUTIONS[OUTPUT_RESOLUTION]
                # تغيير حجم الفيديو النهائي ليناسب الدقة المختارة
                if final_video.size != (width, height):
                    update_progress(f"جاري تغيير حجم الفيديو إلى {width}×{height}...")
                    # استخدام الطريقة العادية دائمًا لتجنب مشاكل التوافق مع GPU
                    try:
                        final_video = final_video.resize(width=width, height=height)
                        print("تم تغيير حجم الفيديو بنجاح")
                    except Exception as e:
                        print(f"خطأ في تغيير حجم الفيديو: {e}")
                        # استمرار بدون تغيير الحجم في حالة الخطأ

        # إضافة العلامة المائية إلى الفيديو النهائي
        if SHOW_WATERMARK:
            try:
                update_progress("جاري إضافة العلامة المائية...")

                # إنشاء صورة للعلامة المائية باستخدام PIL
                from PIL import Image, ImageDraw, ImageFont

                # تحديد حجم العلامة المائية بناءً على نسبة العرض
                if OUTPUT_RESOLUTION == "16:9":
                    # للفيديو الأفقي، نستخدم عرض أكبر وارتفاع أقل
                    watermark_width = int(width * 0.8)
                    watermark_height = int(height * 0.1)
                else:  # 9:16
                    # للفيديو الطولي، نستخدم عرض أقل وارتفاع أكبر
                    watermark_width = int(width * 0.9)
                    watermark_height = int(height * 0.08)

                # إنشاء صورة شفافة
                watermark_img = Image.new('RGBA', (watermark_width, watermark_height), (0, 0, 0, 0))

                # إنشاء قلم رسم
                draw = ImageDraw.Draw(watermark_img)

                # تحديد حجم الخط بناءً على معامل الحجم ونسبة العرض
                base_font_size = int(height * 0.07)
                font_size = int(base_font_size * WATERMARK_SIZE_FACTOR)  # تطبيق معامل الحجم

                try:
                    # محاولة تحميل خط Arial
                    font_path = os.path.join(os.environ.get('WINDIR', ''), 'Fonts', 'arial.ttf')
                    if os.path.exists(font_path):
                        font = ImageFont.truetype(font_path, font_size)
                    else:
                        # محاولة تحميل خط النظام
                        font = ImageFont.truetype("arial.ttf", font_size)
                except:
                    # استخدام الخط الافتراضي إذا فشلت المحاولات السابقة
                    font = ImageFont.load_default()

                # حساب موقع النص لتوسيطه
                # استخدام طريقة مختلفة لحساب أبعاد النص حسب إصدار PIL
                if hasattr(draw, 'textsize'):
                    text_width, text_height = draw.textsize(WATERMARK_TEXT, font=font)
                elif hasattr(font, 'getsize'):
                    text_width, text_height = font.getsize(WATERMARK_TEXT)
                else:
                    # تقدير تقريبي إذا لم تتوفر الطرق السابقة
                    text_width, text_height = watermark_width // 2, watermark_height // 2

                position = ((watermark_width - text_width) // 2, (watermark_height - text_height) // 2)

                # تحديد مستوى الشفافية (0-255)
                opacity = int(WATERMARK_OPACITY * 2.55)  # تحويل من نسبة مئوية إلى قيمة بين 0-255

                # رسم النص باللون الأبيض مع الشفافية المحددة
                draw.text(position, WATERMARK_TEXT, fill=(255, 255, 255, opacity), font=font)

                # حفظ الصورة مؤقتاً
                temp_watermark_path = os.path.join(OUT_DIR, "watermark_temp.png")
                watermark_img.save(temp_watermark_path)

                # تحميل الصورة كـ ImageClip
                watermark_clip = ImageClip(temp_watermark_path).set_duration(final_video.duration)

                # وضع العلامة المائية في أسفل الفيديو مع ترك مسافة من الحافة
                watermark_y = int(height * 0.9)  # 90% من ارتفاع الفيديو (أقرب للأسفل)

                # ضمان عدم خروج العلامة المائية عن حدود الفيديو
                watermark_height = watermark_img.size[1]  # ارتفاع صورة العلامة المائية
                max_y = height - watermark_height - 10  # ترك هامش 10 بكسل من الأسفل

                # التأكد من أن العلامة المائية لا تتجاوز حدود الفيديو
                watermark_y = min(watermark_y, max_y)

                watermark = watermark_clip.set_position(('center', watermark_y))

                # إنشاء فيديو جديد مع العلامة المائية
                final_video = CompositeVideoClip([final_video, watermark])
                print("تمت إضافة العلامة المائية إلى الفيديو النهائي")

                # حذف الملف المؤقت
                try:
                    os.remove(temp_watermark_path)
                except:
                    pass

            except Exception as e:
                print(f"خطأ في إضافة العلامة المائية إلى الفيديو النهائي: {e}")

        # التحقق من حالة الإيقاف
        if STOP_REQUESTED or STOP_ALL_OPERATIONS:
            print("تم إلغاء إنشاء الفيديو بسبب طلب الإيقاف")
            return

        update_progress("جاري حفظ الفيديو...")

        # استخدام إعدادات الترميز السريعة إذا تم تفعيل الوضع السريع
        if USE_FAST_ENCODING:
            update_progress("جاري حفظ الفيديو (وضع سريع)...")

            # تحديث الواجهة قبل العملية الثقيلة
            root.update_idletasks()

            # تحرير الذاكرة قبل الترميز
            import gc
            gc.collect(2)

            # محاولة الترميز مع التعامل مع أخطاء GPU
            try:
                # استخدام try/except للتعامل مع الإصدارات المختلفة من MoviePy
                try:
                    # محاولة استخدام معلمة logger (للإصدارات الحديثة)
                    final_video.write_videofile(out_path, **FAST_VIDEO_ENCODING_SETTINGS, verbose=False, logger=None)
                except TypeError:
                    # إذا فشلت، جرب بدون معلمة logger (للإصدارات القديمة)
                    final_video.write_videofile(out_path, **FAST_VIDEO_ENCODING_SETTINGS, verbose=False)
            except Exception as e:
                # التحقق من أخطاء GPU المحددة
                error_msg = str(e).lower()
                if any(gpu_error in error_msg for gpu_error in ['nvenc', 'amf', 'qsv', 'gpu', 'nvidia', 'amd', 'intel']):
                    print(f"تم اكتشاف خطأ في تسريع GPU: {e}")
                    print("سيتم التراجع إلى الترميز العادي...")

                    # التراجع إلى الترميز العادي
                    fallback_to_cpu_encoding()

                    # إعادة المحاولة بالترميز العادي
                    update_progress("جاري إعادة حفظ الفيديو بالترميز العادي...")
                    try:
                        final_video.write_videofile(out_path, **FAST_VIDEO_ENCODING_SETTINGS, verbose=False, logger=None)
                    except TypeError:
                        final_video.write_videofile(out_path, **FAST_VIDEO_ENCODING_SETTINGS, verbose=False)
                else:
                    # إذا لم يكن خطأ GPU، أعد رفع الاستثناء
                    raise e
        else:
            update_progress("جاري حفظ الفيديو (جودة عالية)...")

            # تحديث الواجهة قبل العملية الثقيلة
            root.update_idletasks()

            # تحرير الذاكرة قبل الترميز
            import gc
            gc.collect(2)

            # محاولة الترميز مع التعامل مع أخطاء GPU
            try:
                # استخدام try/except للتعامل مع الإصدارات المختلفة من MoviePy
                try:
                    # محاولة استخدام معلمة logger (للإصدارات الحديثة)
                    final_video.write_videofile(out_path, **VIDEO_ENCODING_SETTINGS, verbose=False, logger=None)
                except TypeError:
                    # إذا فشلت، جرب بدون معلمة logger (للإصدارات القديمة)
                    final_video.write_videofile(out_path, **VIDEO_ENCODING_SETTINGS, verbose=False)
            except Exception as e:
                # التحقق من أخطاء GPU المحددة
                error_msg = str(e).lower()
                if any(gpu_error in error_msg for gpu_error in ['nvenc', 'amf', 'qsv', 'gpu', 'nvidia', 'amd', 'intel']):
                    print(f"تم اكتشاف خطأ في تسريع GPU: {e}")
                    print("سيتم التراجع إلى الترميز العادي...")

                    # التراجع إلى الترميز العادي
                    fallback_to_cpu_encoding()

                    # إعادة المحاولة بالترميز العادي
                    update_progress("جاري إعادة حفظ الفيديو بالترميز العادي...")
                    try:
                        final_video.write_videofile(out_path, **VIDEO_ENCODING_SETTINGS, verbose=False, logger=None)
                    except TypeError:
                        final_video.write_videofile(out_path, **VIDEO_ENCODING_SETTINGS, verbose=False)
                else:
                    # إذا لم يكن خطأ GPU، أعد رفع الاستثناء
                    raise e

        # إنشاء ملفات الترجمة
        update_progress("جاري إنشاء ملفات الترجمة...")
        # إذا تم تحديد ملف ترجمة، لا تقم بإنشاء ملفات ترجمة جديدة
        if not translation_file:
            for lang_id, trans_texts in translations.items():
                srt_path = os.path.join(EXEC_DIR, f"translation_{lang_id}.srt")
                create_srt_file(trans_texts, durations, srt_path)

        update_progress("اكتمل!")
        # تحديث قائمة ملفات الترجمة بعد إنشاء ملفات جديدة
        refresh_translation_files()

        # عرض رسالة النجاح فقط إذا لم نكن في وضع إنشاء فيديوهات متعددة
        if not SKIP_SUCCESS_MESSAGES:
            messagebox.showinfo("تم", f"تم إنشاء الفيديو والترجمات بنجاح!\nالفيديو: {out_path}")

    except Exception as e:
        print(f"خطأ: {str(e)}")
        messagebox.showerror("خطأ", f"حدث خطأ غير متوقع:\n{str(e)}")
        update_progress("حدث خطأ.")

    finally:
        progress_bar["value"] = 0
        update_progress("جاهز.")


# ───────────────────────────────────────────────────────────────────────────────
# 5) واجهة المستخدم الرسومية
# ───────────────────────────────────────────────────────────────────────────────


# --- واجهة المستخدم مع خلفية صورة ---
root = tk.Tk()
root.title("Quran Video Generator")
root.resizable(True, True)  # السماح بالتحكم في الحجم

# تحميل صورة الخلفية
try:
    bg_image_pil = Image.open(os.path.join(base, "background.png"))
    bg_image_tk = ImageTk.PhotoImage(bg_image_pil)
except Exception as e:
    print(f"تحذير: تعذر تحميل صورة الخلفية: {e}")
    # إنشاء صورة خلفية افتراضية بلون فاتح
    try:
        from PIL import Image, ImageDraw
        bg_width, bg_height = 800, 600
        bg_image_pil = Image.new('RGB', (bg_width, bg_height), color=(240, 240, 240))
        # إضافة بعض التأثيرات البسيطة للخلفية
        draw = ImageDraw.Draw(bg_image_pil)
        # رسم مستطيل في الخلفية
        draw.rectangle([(0, 0), (bg_width, bg_height)], fill=(240, 240, 240), outline=(200, 200, 200), width=10)
        bg_image_tk = ImageTk.PhotoImage(bg_image_pil)
    except Exception as e2:
        print(f"تحذير: تعذر إنشاء صورة خلفية افتراضية: {e2}")
        bg_image_tk = None

# Canvas للخلفية
canvas = tk.Canvas(root, width=bg_image_tk.width() if bg_image_tk else 800, height=bg_image_tk.height() if bg_image_tk else 600, highlightthickness=0)
canvas.pack(fill="both", expand=True)
if bg_image_tk:
    canvas.create_image(0, 0, image=bg_image_tk, anchor="nw")

# إطار شفاف فوق الخلفية لعناصر الواجهة
main_frame = ttk.Frame(canvas, padding="10")
main_window = canvas.create_window(0, 0, anchor="nw", window=main_frame)

# تحديث حجم الإطار مع تغيير حجم النافذة
def resize_bg(event):
    if bg_image_tk:
        canvas.config(width=event.width, height=event.height)
        canvas.coords(main_window, event.width//2, event.height//2)
        canvas.itemconfig(main_window, anchor="center")
    else:
        canvas.config(width=event.width, height=event.height)
        canvas.coords(main_window, event.width//2, event.height//2)
        canvas.itemconfig(main_window, anchor="center")
root.bind("<Configure>", resize_bg)

# إعداد نمط ttk
# إعداد نمط ttk عصري

# إعداد نمط ttk عصري (يجب أن يكون بعد تعريف progress_frame)
style = ttk.Style()
style.theme_use("clam")
accent_color = "#4fd8e6"         # أزرق سماوي زاهي
bg_color = "#787ac9"            # أبيض مزرق مريح
fg_color = "#4c79b0"            # أزرق رمادي هادئ للنصوص
button_color = "#ffb74d"         # برتقالي فاتح جذاب
button_fg = "#6ddb54"            # نفس لون النصوص
combobox_bg = "#d42d2db8"          # أزرق سماوي فاتح جدًا
combobox_fg = fg_color
progress_color = "#4fd8e6"       # أزرق سماوي زاهي
shadow_color = "#b0bec5"         # رمادي فاتح للظل

try:
    cairo_font = ("Cairo", 13)
except:
    cairo_font = ("Arial", 13)

style.configure("TFrame", background=bg_color)
style.configure("TLabel", background=bg_color, foreground=fg_color, font=cairo_font)
style.configure("TButton", background=button_color, foreground=button_fg, font=("Cairo", 13, "bold"), borderwidth=0, focusthickness=3, focuscolor=accent_color, padding=8)
style.map("TButton",
    background=[('active', accent_color), ('pressed', accent_color)],
    foreground=[('active', fg_color), ('pressed', fg_color)]
)
from tkinter import font as tkfont
# زر صغير للأزرار ▲ ▼ مع شكل مختلف (لون مميز، خط غامق، حواف دائرية)
style.configure(
    "Small.TButton",
    font=("Cairo", 8, "bold"),
    padding=0,
    background="#e3f2fd",
    foreground="#2d3a4a",
    borderwidth=1,
    relief="raised"
)
style.map(
    "Small.TButton",
    background=[('active', '#b2ebf2'), ('pressed', '#4fd8e6')],
    foreground=[('active', '#2d3a4a'), ('pressed', '#2d3a4a')],
    relief=[('pressed', 'sunken'), ('!pressed', 'raised')]
)
style.configure("TCombobox", fieldbackground=combobox_bg, background=combobox_bg, foreground=combobox_fg, font=cairo_font, padding=8)
style.map("TCombobox",
    fieldbackground=[('readonly', combobox_bg)],
    background=[('readonly', combobox_bg)],
    foreground=[('readonly', combobox_fg)]
)
style.configure("TProgressbar", troughcolor="#e3f2fd", background=progress_color, thickness=8, bordercolor=bg_color, lightcolor=progress_color, darkcolor=progress_color)

# --- استبدال progress_label بعناصر Canvas مع ظل للنص ---
def draw_text_with_shadow(canvas, x, y, text, font, fill, shadowcolor, anchor="center"):
    # رسم الظل
    canvas.create_text(x+2, y+2, text=text, font=font, fill=shadowcolor, anchor=anchor)
    # رسم النص الأصلي فوق الظل
    canvas.create_text(x, y, text=text, font=font, fill=fill, anchor=anchor)

# حذف جميع العناصر القديمة من progress_frame
def replace_progress_and_yaser_labels():
    # حذف جميع العناصر من progress_frame
    for widget in progress_frame.grid_slaves():
        widget.grid_forget()

    # حذف أي عناصر تم وضعها باستخدام place
    for widget in progress_frame.place_slaves():
        widget.place_forget()

    # حذف أي عناصر تم وضعها باستخدام pack
    for widget in progress_frame.pack_slaves():
        widget.pack_forget()

    # Canvas لشريط التقدم
    global progress_canvas
    progress_canvas = tk.Canvas(progress_frame, width=320, height=60, bg=bg_color, highlightthickness=0)
    progress_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E))

    # شريط التقدم العصري
    global progress_bar
    progress_bar = ttk.Progressbar(progress_frame, mode="determinate", length=300, style="TProgressbar")
    progress_bar.place(x=10, y=10, width=300, height=16)

    # رسم progress label مع ظل
    def set_progress_label(text):
        progress_canvas.delete("progress_text")
        draw_text_with_shadow(progress_canvas, 160, 40, text, ("Cairo", 14, "bold"), fg_color, shadow_color, anchor="center")
        progress_canvas.addtag_withtag("progress_text", "all")
    global set_progress_label_fn
    set_progress_label_fn = set_progress_label
    set_progress_label("جاهز.")

    # تحديث دالة update_progress لاستخدام set_progress_label
    def update_progress_new(progress_text, value=None):
        set_progress_label(progress_text)
        if value is not None:
            progress_bar["value"] = value
        root.update_idletasks()
    global update_progress
    update_progress = update_progress_new


# القارئ (تكبير الخط الأسود بنسبة 20%)
big_label_font = ("Cairo", int(13 * 1.2), "bold")
reciter_label = ttk.Label(main_frame, text="اختر القارئ:", foreground="#2d3a4a", font=big_label_font)
reciter_label.grid(row=0, column=0, sticky=tk.W, pady=5)
reciter_var = tk.StringVar(value=RECITERS_DISPLAY[0])
reciter_combo = ttk.Combobox(main_frame, textvariable=reciter_var, values=RECITERS_DISPLAY, state="readonly", width=40, font=big_label_font)
reciter_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5)

# السورة (تكبير الخط الأسود بنسبة 20%)
surah_label = ttk.Label(main_frame, text="اختر السورة:", foreground="#2d3a4a", font=big_label_font)
surah_label.grid(row=1, column=0, sticky=tk.W, pady=5)
surah_var = tk.StringVar(value=SURAH_NAMES[0])
surah_combo = ttk.Combobox(main_frame, textvariable=surah_var, values=SURAH_NAMES, state="readonly", width=40, font=big_label_font)
surah_combo.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5)


# رقم الآية (من) مع أزرار فوق وتحت
ttk.Label(main_frame, text="من آية:").grid(row=2, column=0, sticky=tk.W, pady=5)
start_ayah_var = tk.StringVar(value="1")
start_ayah_frame = ttk.Frame(main_frame)
start_ayah_frame.grid(row=2, column=1, sticky=tk.W, pady=5)
start_ayah_entry = ttk.Entry(start_ayah_frame, textvariable=start_ayah_var, width=10)
start_ayah_entry.grid(row=0, column=0)

# تعريف الأزرار أولاً ثم ربط الأحداث
import time
import threading
def repeat_action(action, var, min_value, max_value, step, delay=0.08):
    def runner():
        while getattr(runner, 'running', True):
            try:
                v = int(var.get())
            except:
                v = min_value
            if action == 'inc' and v < max_value:
                var.set(str(v+step))
            elif action == 'dec' and v > min_value:
                var.set(str(v-step))
            else:
                break
            time.sleep(delay)
    runner.running = True
    t = threading.Thread(target=runner)
    t.start()
    return runner

def inc_start(event=None):
    v = int(start_ayah_var.get() or 1)
    max_ayah = VERSE_COUNTS[SURAH_NAMES.index(surah_var.get())+1]
    if v < max_ayah:
        start_ayah_var.set(str(v+1))
def dec_start(event=None):
    v = int(start_ayah_var.get() or 1)
    if v > 1:
        start_ayah_var.set(str(v-1))
def inc_start_hold(event):
    max_ayah = VERSE_COUNTS[SURAH_NAMES.index(surah_var.get())+1]
    event.widget._repeat_runner = repeat_action('inc', start_ayah_var, 1, max_ayah, 1)
def dec_start_hold(event):
    event.widget._repeat_runner = repeat_action('dec', start_ayah_var, 1, VERSE_COUNTS[SURAH_NAMES.index(surah_var.get())+1], 1)
def stop_repeat(event):
    if hasattr(event.widget, '_repeat_runner'):
        event.widget._repeat_runner.running = False

start_up_btn = ttk.Button(start_ayah_frame, text="▲", width=1, command=inc_start, style="Small.TButton")
start_up_btn.grid(row=0, column=1)
start_down_btn = ttk.Button(start_ayah_frame, text="▼", width=1, command=dec_start, style="Small.TButton")
start_down_btn.grid(row=0, column=2)
start_up_btn.bind('<ButtonPress-1>', inc_start_hold)
start_up_btn.bind('<ButtonRelease-1>', stop_repeat)
start_down_btn.bind('<ButtonPress-1>', dec_start_hold)
start_down_btn.bind('<ButtonRelease-1>', stop_repeat)

# رقم الآية (إلى) مع أزرار فوق وتحت
ttk.Label(main_frame, text="إلى آية:").grid(row=3, column=0, sticky=tk.W, pady=5)
end_ayah_var = tk.StringVar(value="1")
end_ayah_frame = ttk.Frame(main_frame)
end_ayah_frame.grid(row=3, column=1, sticky=tk.W, pady=5)
end_ayah_entry = ttk.Entry(end_ayah_frame, textvariable=end_ayah_var, width=10)
end_ayah_entry.grid(row=0, column=0)

# تعريف أزرار النهاية أولاً
end_up_btn = ttk.Button(end_ayah_frame, text="▲", width=1, command=None, style="Small.TButton")
end_up_btn.grid(row=0, column=1)
end_down_btn = ttk.Button(end_ayah_frame, text="▼", width=1, command=None, style="Small.TButton")
end_down_btn.grid(row=0, column=2)

def inc_end(event=None):
    v = int(end_ayah_var.get() or 1)
    max_ayah = VERSE_COUNTS[SURAH_NAMES.index(surah_var.get())+1]
    if v < max_ayah:
        end_ayah_var.set(str(v+1))
def dec_end(event=None):
    v = int(end_ayah_var.get() or 1)
    if v > 1:
        end_ayah_var.set(str(v-1))
def inc_end_hold(event):
    max_ayah = VERSE_COUNTS[SURAH_NAMES.index(surah_var.get())+1]
    event.widget._repeat_runner = repeat_action('inc', end_ayah_var, 1, max_ayah, 1)
def dec_end_hold(event):
    event.widget._repeat_runner = repeat_action('dec', end_ayah_var, 1, VERSE_COUNTS[SURAH_NAMES.index(surah_var.get())+1], 1)

# ربط الأحداث بعد تعريف الأزرار
end_up_btn.config(command=inc_end)
end_down_btn.config(command=dec_end)
end_up_btn.bind('<ButtonPress-1>', inc_end_hold)
end_up_btn.bind('<ButtonRelease-1>', stop_repeat)
end_down_btn.bind('<ButtonPress-1>', dec_end_hold)
end_down_btn.bind('<ButtonRelease-1>', stop_repeat)

# جلب الترجمات المتاحة
fetch_translations()

# اختيار الترجمة
ttk.Label(main_frame, text="اختر الترجمة:").grid(row=4, column=0, sticky=tk.W, pady=5)
translation_var = tk.StringVar(value=TRANSLATIONS_DISPLAY[0] if TRANSLATIONS_DISPLAY else "")
translation_combo = ttk.Combobox(main_frame, textvariable=translation_var, values=TRANSLATIONS_DISPLAY, state="readonly", width=40)
translation_combo.grid(row=4, column=1, sticky=(tk.W, tk.E), pady=5)

# اختيار ملف الترجمة
def get_translation_files():
    """الحصول على قائمة ملفات الترجمة المتوفرة"""
    translation_files = []
    try:
        for file in os.listdir(EXEC_DIR):
            if file.startswith("translation_") and file.endswith(".srt"):
                translation_files.append(file)
        return translation_files
    except Exception as e:
        print(f"خطأ في قراءة ملفات الترجمة: {e}")
        return []

# دالة لتنزيل الترجمات للسورة المحددة - نسخة محسنة لتقليل التهنيج
def download_translations(surah_idx, start_ayah, end_ayah):
    """تنزيل الترجمات للسورة المحددة - نسخة محسنة لتقليل التهنيج"""
    global STOP_REQUESTED, STOP_ALL_OPERATIONS

    # قائمة معرفات الترجمات المطلوبة
    required_translations = {
        "ar": "ar.muyassar",
        "en": "en.sahih",
        "fr": "fr.hamidullah",
        "it": "it.piccardo",
        "ru": "ru.kuliev",
        "tr": "tr.diyanet"
    }

    # تنزيل الترجمات
    update_progress("جاري تنزيل الترجمات...", 10)
    root.update_idletasks()  # تحديث الواجهة لمنع التهنيج

    # إنشاء قاموس لتخزين نصوص الترجمات
    translations = {}

    # إنشاء session واحدة لجميع الطلبات
    session = requests.Session()

    # تنزيل كل ترجمة
    for lang_code, translation_id in required_translations.items():
        # التحقق من حالة الإيقاف
        if STOP_REQUESTED or STOP_ALL_OPERATIONS:
            print("تم إلغاء تنزيل الترجمات بسبب طلب الإيقاف")
            return {}

        update_progress(f"جاري تنزيل ترجمة {lang_code}...", 20 + (10 * list(required_translations.keys()).index(lang_code)))
        root.update_idletasks()  # تحديث الواجهة لمنع التهنيج

        # تنزيل الترجمة
        try:
            translation_texts = []
            for ayah in range(start_ayah, end_ayah + 1):
                # التحقق من حالة الإيقاف
                if STOP_REQUESTED or STOP_ALL_OPERATIONS:
                    print("تم إلغاء تنزيل الترجمات بسبب طلب الإيقاف")
                    return {}

                trans_text = get_ayah_translation(surah_idx, ayah, translation_id)
                translation_texts.append(trans_text)

                # تحديث الواجهة كل 3 آيات لتقليل التهنيج مع الحفاظ على الأداء
                if ayah % 3 == 0:
                    root.update_idletasks()

            translations[lang_code] = translation_texts

            # تحرير الذاكرة بعد كل لغة
            import gc
            gc.collect(0)  # تنظيف الجيل الأول فقط للحفاظ على الأداء

        except Exception as e:
            print(f"خطأ في تنزيل ترجمة {lang_code}: {e}")
            root.update_idletasks()  # تحديث الواجهة لمنع التهنيج

    # إنشاء ملفات الترجمة
    update_progress("جاري إنشاء ملفات الترجمة...", 80)
    root.update_idletasks()  # تحديث الواجهة لمنع التهنيج

    # الحصول على مدد الآيات
    durations = []
    for ayah in range(start_ayah, end_ayah + 1):
        # التحقق من حالة الإيقاف
        if STOP_REQUESTED or STOP_ALL_OPERATIONS:
            print("تم إلغاء تنزيل الترجمات بسبب طلب الإيقاف")
            return {}

        try:
            # تنزيل الصوت للحصول على المدة
            audio_path = download_audio(RECITERS_MAP.get(reciter_var.get(), ""), surah_idx, ayah, ayah - start_ayah)
            audio_clip = AudioFileClip(audio_path)
            durations.append(audio_clip.duration)
            audio_clip.close()

            # تحديث الواجهة كل 3 آيات لتقليل التهنيج مع الحفاظ على الأداء
            if ayah % 3 == 0:
                root.update_idletasks()

        except Exception as e:
            print(f"خطأ في الحصول على مدة الآية {ayah}: {e}")
            # استخدام مدة افتراضية في حالة الخطأ
            durations.append(5.0)
            root.update_idletasks()  # تحديث الواجهة لمنع التهنيج

    # إنشاء ملفات الترجمة
    for lang_id, trans_texts in translations.items():
        # التحقق من حالة الإيقاف
        if STOP_REQUESTED or STOP_ALL_OPERATIONS:
            print("تم إلغاء تنزيل الترجمات بسبب طلب الإيقاف")
            return {}

        translation_id = required_translations[lang_id]
        translator_name = translation_id.split('.')[1]
        srt_path = os.path.join(EXEC_DIR, f"translation_{lang_id}.{translator_name}.srt")
        create_srt_file(trans_texts, durations, srt_path)
        root.update_idletasks()  # تحديث الواجهة لمنع التهنيج

    update_progress("تم تنزيل الترجمات بنجاح!", 100)
    root.update_idletasks()  # تحديث الواجهة لمنع التهنيج

    # تحرير الذاكرة بعد الانتهاء
    import gc
    gc.collect(2)  # تنظيف جميع الأجيال

    return translations

def refresh_translation_files():
    """تحديث قائمة ملفات الترجمة"""
    try:
        translation_files = get_translation_files()
        translation_file_combo['values'] = translation_files
        if translation_files and not translation_file_var.get():
            translation_file_var.set(translation_files[0])
        elif translation_file_var.get() not in translation_files:
            if translation_files:
                translation_file_var.set(translation_files[0])
            else:
                translation_file_var.set("")
    except Exception as e:
        print(f"خطأ في تحديث قائمة ملفات الترجمة: {e}")

ttk.Label(main_frame, text="اختر ملف الترجمة:").grid(row=5, column=0, sticky=tk.W, pady=5)
translation_file_var = tk.StringVar()
translation_files = get_translation_files()
if translation_files:
    translation_file_var.set(translation_files[0])

# إنشاء إطار لاحتواء القائمة المنسدلة وزر التحديث
translation_file_frame = ttk.Frame(main_frame)
translation_file_frame.grid(row=5, column=1, sticky=(tk.W, tk.E), pady=5)

translation_file_combo = ttk.Combobox(translation_file_frame, textvariable=translation_file_var, values=translation_files, state="readonly", width=38)
translation_file_combo.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

# زر تحديث قائمة ملفات الترجمة
refresh_button = ttk.Button(translation_file_frame, text="تحديث", command=refresh_translation_files, width=5)
refresh_button.grid(row=0, column=1, sticky=tk.E)

# إضافة عنصر لعرض حالة العملية
status_frame = ttk.Frame(main_frame)
status_frame.grid(row=8, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
status_label = ttk.Label(status_frame, text="جاهز", foreground="#2d3a4a")
status_label.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=5)

# اختيار فيديو الخلفية
def choose_background_video():
    global BACKGROUND_VIDEO_PATH, VIDEO_ASPECT_RATIO
    try:
        file_path = filedialog.askopenfilename(
            title="اختر فيديو الخلفية",
            filetypes=[
                ("ملفات الفيديو", "*.mp4 *.avi *.mkv *.mov"),
                ("كل الملفات", "*.*")
            ]
        )
        if file_path:
            # التحقق من صحة ملف الفيديو
            try:
                # محاولة فتح الفيديو للتأكد من أنه صالح
                # استخدام try/except للتعامل مع الإصدارات المختلفة من MoviePy
                try:
                    # محاولة استخدام معلمة logger (للإصدارات الحديثة)
                    test_clip = VideoFileClip(file_path, audio=False, verbose=False, logger=None)
                except Exception as e:
                    print(f"خطأ في فتح الفيديو (الطريقة الأولى): {e}")
                    # إذا فشلت، جرب بدون معلمة logger (للإصدارات القديمة)
                    try:
                        test_clip = VideoFileClip(file_path, audio=False, verbose=False)
                    except Exception as e2:
                        print(f"خطأ في فتح الفيديو (الطريقة الثانية): {e2}")
                        # محاولة أخيرة بأقل الخيارات
                        test_clip = VideoFileClip(file_path, audio=False)
                # إغلاق الفيديو بعد التحقق
                test_clip.close()

                # تخزين المسار
                BACKGROUND_VIDEO_PATH = file_path
                print(f"تم تعيين مسار الخلفية إلى: {BACKGROUND_VIDEO_PATH}")
                video_path_label.config(text=os.path.basename(file_path))
                # تغيير لون النص للإشارة إلى أن الفيديو تم اختياره
                video_path_label.config(foreground="#008000")  # لون أخضر
                # تحديث حالة التقدم
                status_label.config(text=f"تم اختيار فيديو الخلفية: {os.path.basename(file_path)}")

                # حفظ مسار الخلفية في ملف لاستخدامه في المرات القادمة
                try:
                    with open(os.path.join(EXEC_DIR, "background_path.txt"), "w", encoding="utf-8") as f:
                        f.write(file_path)
                    print(f"تم حفظ مسار الخلفية في ملف: {os.path.join(EXEC_DIR, 'background_path.txt')}")
                except Exception as e:
                    print(f"خطأ في حفظ مسار الخلفية: {e}")

            except Exception as e:
                messagebox.showerror("خطأ", f"الملف المحدد ليس ملف فيديو صالح أو لا يمكن قراءته:\n{str(e)}")
                print(f"خطأ في التحقق من صحة ملف الفيديو: {e}")
                # لا نقوم بتعيين المسار في حالة الخطأ
                return

            # تحديد نسبة العرض إلى الارتفاع للفيديو المختار
            update_progress("جاري تحليل الفيديو...", 50)

            # تحليل الفيديو في خيط منفصل لتجنب تجميد الواجهة
            def analyze_video_thread():
                global VIDEO_ASPECT_RATIO
                try:
                    # تحميل الفيديو بشكل مؤقت لمعرفة أبعاده
                    video = VideoFileClip(file_path, audio=False)
                    width, height = video.size

                    # حساب نسبة العرض إلى الارتفاع
                    if width > 0 and height > 0:
                        # تبسيط النسبة
                        gcd = math.gcd(width, height)
                        aspect_width = width // gcd
                        aspect_height = height // gcd

                        # تحديد نسبة العرض إلى الارتفاع
                        if abs(aspect_width/aspect_height - 16/9) < 0.1:
                            VIDEO_ASPECT_RATIO = "16:9"
                        elif abs(aspect_width/aspect_height - 4/3) < 0.1:
                            VIDEO_ASPECT_RATIO = "4:3"
                        elif abs(aspect_width/aspect_height - 9/16) < 0.1:
                            VIDEO_ASPECT_RATIO = "9:16"  # نسبة الهاتف المحمول
                        else:
                            VIDEO_ASPECT_RATIO = f"{aspect_width}:{aspect_height}"

                        # إغلاق الفيديو لتحرير الذاكرة
                        video.close()

                        # تحديث الواجهة في الخيط الرئيسي
                        root.after(0, lambda: update_progress(f"تم اختيار الفيديو: {os.path.basename(file_path)} (نسبة العرض: {VIDEO_ASPECT_RATIO})", 0))

                        # تحديث قيمة نسبة العرض في القائمة المنسدلة إذا كانت موجودة
                        if 'aspect_ratio_var' in globals():
                            def update_aspect_ratio():
                                try:
                                    # تحديث القيمة في القائمة المنسدلة إذا كانت القيمة موجودة في القائمة
                                    values = aspect_ratio_combo['values']
                                    if VIDEO_ASPECT_RATIO in values:
                                        aspect_ratio_var.set(VIDEO_ASPECT_RATIO)
                                except Exception as e:
                                    print(f"خطأ في تحديث نسبة العرض: {e}")

                            root.after(0, update_aspect_ratio)

                except Exception as e:
                    print(f"خطأ في تحليل الفيديو: {e}")
                    root.after(0, lambda: update_progress(f"تم اختيار الفيديو: {os.path.basename(file_path)}", 0))

                # تنظيف الذاكرة
                import gc
                gc.collect()

            # بدء خيط تحليل الفيديو
            video_thread = threading.Thread(target=analyze_video_thread)
            video_thread.daemon = True
            video_thread.start()

    except Exception as e:
        # التعامل مع أي خطأ قد يحدث
        messagebox.showerror("خطأ", f"حدث خطأ أثناء اختيار الفيديو: {str(e)}")
        update_progress("حدث خطأ أثناء اختيار الفيديو.", 0)

video_frame = ttk.Frame(main_frame)
video_frame.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

video_label = ttk.Label(video_frame, text="فيديو الخلفية:", foreground="#2d3a4a")
video_label.grid(row=0, column=0, sticky=tk.W, pady=5)

# تحديث نص مسار الفيديو إذا كان هناك مسار محفوظ
if BACKGROUND_VIDEO_PATH and os.path.exists(BACKGROUND_VIDEO_PATH):
    video_path_text = os.path.basename(BACKGROUND_VIDEO_PATH)
    video_path_color = "#008000"  # لون أخضر
else:
    video_path_text = "لم يتم اختيار فيديو"
    video_path_color = "#777777"  # لون رمادي

video_path_label = ttk.Label(video_frame, text=video_path_text, foreground=video_path_color)
video_path_label.grid(row=0, column=1, sticky=tk.W, pady=5)

video_button = ttk.Button(video_frame, text="اختيار فيديو", command=choose_background_video)
video_button.grid(row=0, column=2, sticky=tk.E, pady=5, padx=5)

# إضافة قائمة منسدلة لاختيار نسبة العرض إلى الارتفاع ودقة الفيديو
aspect_ratio_frame = ttk.Frame(video_frame)
aspect_ratio_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)

# قائمة نسب العرض المتاحة
ASPECT_RATIOS = ["16:9", "9:16"]

def on_aspect_ratio_change(*args):
    """تغيير نسبة العرض إلى الارتفاع"""
    try:
        global VIDEO_ASPECT_RATIO, OUTPUT_RESOLUTION
        VIDEO_ASPECT_RATIO = aspect_ratio_var.get()

        # تحديث قائمة الدقات المتاحة بناءً على نسبة العرض الجديدة
        if VIDEO_ASPECT_RATIO == "16:9":
            available_resolutions = [res for res in VIDEO_RESOLUTIONS.keys() if "(16:9)" in res]
            default_resolution = "720p (16:9)"
        else:  # 9:16
            available_resolutions = [res for res in VIDEO_RESOLUTIONS.keys() if "(9:16)" in res]
            default_resolution = "720p (9:16)"

        # تحديث قائمة الدقات المتاحة
        resolution_combo['values'] = available_resolutions

        # تحديث الدقة المختارة إذا لم تكن متوافقة مع النسبة الجديدة
        current_resolution = resolution_var.get()
        if current_resolution not in available_resolutions:
            resolution_var.set(default_resolution)
            OUTPUT_RESOLUTION = default_resolution

        status_label.config(text=f"تم تغيير نسبة العرض إلى: {VIDEO_ASPECT_RATIO}")
        print(f"تم تغيير نسبة العرض إلى: {VIDEO_ASPECT_RATIO}")
        root.update_idletasks()
    except Exception as e:
        print(f"خطأ في تغيير نسبة العرض: {e}")

# إنشاء القائمة المنسدلة لنسبة العرض
aspect_ratio_label = ttk.Label(aspect_ratio_frame, text="نسبة العرض:", foreground="#2d3a4a")
aspect_ratio_label.grid(row=0, column=0, sticky=tk.W, pady=5)

aspect_ratio_var = tk.StringVar(value=VIDEO_ASPECT_RATIO)
aspect_ratio_combo = ttk.Combobox(aspect_ratio_frame, textvariable=aspect_ratio_var, values=ASPECT_RATIOS, state="readonly", width=10)
aspect_ratio_combo.grid(row=0, column=1, sticky=tk.W, pady=5)
aspect_ratio_var.trace_add('write', on_aspect_ratio_change)

# إضافة خيار اختيار دقة الفيديو
resolution_label = ttk.Label(aspect_ratio_frame, text="دقة الفيديو:", foreground="#2d3a4a")
resolution_label.grid(row=0, column=2, sticky=tk.W, pady=5, padx=(20, 0))

# قائمة الدقات المتاحة
AVAILABLE_RESOLUTIONS = list(VIDEO_RESOLUTIONS.keys())

resolution_var = tk.StringVar(value=OUTPUT_RESOLUTION)
resolution_combo = ttk.Combobox(aspect_ratio_frame, textvariable=resolution_var, values=AVAILABLE_RESOLUTIONS, state="readonly", width=15)
resolution_combo.grid(row=0, column=3, sticky=tk.W, pady=5, padx=(5, 0))

def on_resolution_change(*args):
    """تغيير دقة الفيديو"""
    try:
        global OUTPUT_RESOLUTION
        OUTPUT_RESOLUTION = resolution_var.get()
        status_label.config(text=f"تم تغيير دقة الفيديو إلى: {OUTPUT_RESOLUTION}")
        print(f"تم تغيير دقة الفيديو إلى: {OUTPUT_RESOLUTION}")
        save_user_settings()  # حفظ الإعدادات
    except Exception as e:
        print(f"خطأ في تغيير دقة الفيديو: {e}")

resolution_var.trace_add('write', on_resolution_change)

# إضافة خيار وضع الترميز (سريع أو جودة عالية)
encoding_mode_frame = ttk.Frame(aspect_ratio_frame)
encoding_mode_frame.grid(row=1, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=5)

encoding_mode_label = ttk.Label(encoding_mode_frame, text="وضع الترميز:", foreground="#2d3a4a")
encoding_mode_label.grid(row=0, column=0, sticky=tk.W, pady=5)

ENCODING_MODES = ["سريع (جودة متوسطة)", "جودة عالية (بطيء)"]
encoding_mode_var = tk.StringVar(value=ENCODING_MODES[0] if USE_FAST_ENCODING else ENCODING_MODES[1])
encoding_mode_combo = ttk.Combobox(encoding_mode_frame, textvariable=encoding_mode_var, values=ENCODING_MODES, state="readonly", width=20)
encoding_mode_combo.grid(row=0, column=1, sticky=tk.W, pady=5, padx=(5, 0))

def on_encoding_mode_change(*args):
    """تغيير وضع الترميز"""
    try:
        global USE_FAST_ENCODING
        selected_mode = encoding_mode_var.get()
        USE_FAST_ENCODING = (selected_mode == ENCODING_MODES[0])
        mode_text = "سريع" if USE_FAST_ENCODING else "جودة عالية"
        status_label.config(text=f"تم تغيير وضع الترميز إلى: {mode_text}")
        print(f"تم تغيير وضع الترميز إلى: {mode_text}")
        save_user_settings()  # حفظ الإعدادات
    except Exception as e:
        print(f"خطأ في تغيير وضع الترميز: {e}")

encoding_mode_var.trace_add('write', on_encoding_mode_change)

# إضافة معلومات عن تسريع GPU مع تفاصيل أكثر
gpu_status_text = ""
if GPU_TYPE == 'nvidia':
    gpu_status_text = "🚀 NVIDIA NVENC مفعل"
elif GPU_TYPE == 'amd':
    gpu_status_text = "🚀 AMD AMF مفعل"
elif GPU_TYPE == 'intel':
    gpu_status_text = "🚀 Intel QSV مفعل"
else:
    gpu_status_text = "⚙️ ترميز عادي (libx264)"

gpu_info_label = ttk.Label(encoding_mode_frame, text=f"تسريع GPU: {gpu_status_text}", foreground="#2d3a4a")
gpu_info_label.grid(row=0, column=2, sticky=tk.W, pady=5, padx=(20, 0))

# إضافة إطار التحكم في الخط والعلامة المائية
font_frame = ttk.Frame(main_frame)
font_frame.grid(row=7, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

# التحكم في حجم الخط
font_size_label = ttk.Label(font_frame, text="حجم الخط:", foreground="#2d3a4a")
font_size_label.grid(row=0, column=0, sticky=tk.W, pady=5)

def increase_font_size():
    """زيادة حجم الخط بدون تجميد الواجهة"""
    try:
        global ARABIC_FONT_SIZE_FACTOR
        if ARABIC_FONT_SIZE_FACTOR < 1.0:
            ARABIC_FONT_SIZE_FACTOR += 0.05
            # تقريب القيمة لتجنب الأرقام العشرية الطويلة
            ARABIC_FONT_SIZE_FACTOR = round(ARABIC_FONT_SIZE_FACTOR, 2)
            # تحديث النص فقط بدون أي معالجة إضافية
            font_size_value_label.config(text=f"{int(ARABIC_FONT_SIZE_FACTOR * 100)}%")
            status_label.config(text=f"تم تغيير حجم الخط إلى {int(ARABIC_FONT_SIZE_FACTOR * 100)}%")
            # تحديث الواجهة فوراً
            root.update_idletasks()
    except Exception as e:
        print(f"خطأ في زيادة حجم الخط: {e}")

def decrease_font_size():
    """تقليل حجم الخط بدون تجميد الواجهة"""
    try:
        global ARABIC_FONT_SIZE_FACTOR
        # تغيير الحد الأدنى إلى 0.1 (10%) بدلاً من 0.3 (30%)
        if ARABIC_FONT_SIZE_FACTOR > 0.1:
            ARABIC_FONT_SIZE_FACTOR -= 0.05
            # تقريب القيمة لتجنب الأرقام العشرية الطويلة
            ARABIC_FONT_SIZE_FACTOR = round(ARABIC_FONT_SIZE_FACTOR, 2)
            # تحديث النص فقط بدون أي معالجة إضافية
            font_size_value_label.config(text=f"{int(ARABIC_FONT_SIZE_FACTOR * 100)}%")
            status_label.config(text=f"تم تغيير حجم الخط إلى {int(ARABIC_FONT_SIZE_FACTOR * 100)}%")
            # تحديث الواجهة فوراً
            root.update_idletasks()
    except Exception as e:
        print(f"خطأ في تقليل حجم الخط: {e}")

font_size_frame = ttk.Frame(font_frame)
font_size_frame.grid(row=0, column=1, sticky=tk.W, pady=5)

font_size_value_label = ttk.Label(font_size_frame, text=f"{int(ARABIC_FONT_SIZE_FACTOR * 100)}%", width=5)
font_size_value_label.grid(row=0, column=0, padx=5)

font_size_increase_btn = ttk.Button(font_size_frame, text="+", width=2, command=increase_font_size, style="Small.TButton")
font_size_increase_btn.grid(row=0, column=1, padx=2)

font_size_decrease_btn = ttk.Button(font_size_frame, text="-", width=2, command=decrease_font_size, style="Small.TButton")
font_size_decrease_btn.grid(row=0, column=2, padx=2)

# التحكم في لون الخط
font_color_label = ttk.Label(font_frame, text="لون الخط:", foreground="#2d3a4a")
font_color_label.grid(row=0, column=2, sticky=tk.W, pady=5, padx=(20, 5))

# قائمة الألوان المتاحة
FONT_COLORS = {
    "ذهبي": "#FFD700",
    "أبيض": "#FFFFFF",
    "أخضر": "#00FF00",
    "أزرق": "#00BFFF",
    "أحمر": "#FF4500",
    "برتقالي": "#FFA500",
    "وردي": "#FF69B4"
}

def on_color_change(*args):
    """تغيير لون الخط بدون تجميد الواجهة"""
    try:
        global ARABIC_FONT_COLOR
        color_name = font_color_var.get()
        ARABIC_FONT_COLOR = FONT_COLORS[color_name]
        # تحديث النص فقط بدون أي معالجة إضافية
        status_label.config(text=f"تم تغيير لون الخط إلى {color_name}")
        # تحديث الواجهة فوراً
        root.update_idletasks()
    except Exception as e:
        print(f"خطأ في تغيير لون الخط: {e}")

font_color_var = tk.StringVar(value="ذهبي")
font_color_combo = ttk.Combobox(font_frame, textvariable=font_color_var, values=list(FONT_COLORS.keys()), state="readonly", width=10)
font_color_combo.grid(row=0, column=3, sticky=tk.W, pady=5)
font_color_var.trace_add('write', on_color_change)

# التحكم في العلامة المائية
watermark_frame = ttk.Frame(font_frame)
watermark_frame.grid(row=1, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=5)

watermark_label = ttk.Label(watermark_frame, text="العلامة المائية:", foreground="#2d3a4a")
watermark_label.grid(row=0, column=0, sticky=tk.W, pady=5)

def toggle_watermark():
    """تفعيل/تعطيل العلامة المائية"""
    try:
        global SHOW_WATERMARK
        SHOW_WATERMARK = not SHOW_WATERMARK
        watermark_status = "مفعلة" if SHOW_WATERMARK else "معطلة"
        watermark_status_label.config(text=watermark_status)
        watermark_status_label.config(foreground="#008000" if SHOW_WATERMARK else "#FF0000")
        status_label.config(text=f"العلامة المائية: {watermark_status}")
        root.update_idletasks()
    except Exception as e:
        print(f"خطأ في تغيير حالة العلامة المائية: {e}")

watermark_status = "مفعلة" if SHOW_WATERMARK else "معطلة"
watermark_status_label = ttk.Label(watermark_frame, text=watermark_status, foreground="#008000" if SHOW_WATERMARK else "#FF0000")
watermark_status_label.grid(row=0, column=1, sticky=tk.W, pady=5, padx=(5, 20))

watermark_toggle_btn = ttk.Button(watermark_frame, text="تفعيل/تعطيل", command=toggle_watermark)
watermark_toggle_btn.grid(row=0, column=2, sticky=tk.W, pady=5)

# إضافة حقل لإدخال نص العلامة المائية
watermark_text_label = ttk.Label(watermark_frame, text="نص العلامة المائية:", foreground="#2d3a4a")
watermark_text_label.grid(row=0, column=3, sticky=tk.W, pady=5, padx=(20, 0))

def on_watermark_text_change(*args):
    """تغيير نص العلامة المائية"""
    try:
        global WATERMARK_TEXT
        # الحصول على النص من حقل الإدخال
        text = watermark_text_var.get()

        # معالجة النص العربي لضمان ظهوره بشكل صحيح
        # تحويل النص إلى الترتيب الصحيح للعربية
        WATERMARK_TEXT = text

        status_label.config(text=f"تم تغيير نص العلامة المائية إلى: {WATERMARK_TEXT}")
        root.update_idletasks()
    except Exception as e:
        print(f"خطأ في تغيير نص العلامة المائية: {e}")

# استخدام حقل نص متعدد الأسطر بدلاً من Entry لدعم النص العربي بشكل أفضل
watermark_text_var = tk.StringVar(value=WATERMARK_TEXT)
watermark_text_frame = ttk.Frame(watermark_frame)
watermark_text_frame.grid(row=0, column=4, sticky=tk.W, pady=5, padx=(5, 0))

# استخدام Text بدلاً من Entry لدعم النص العربي بشكل أفضل
watermark_text_entry = tk.Text(watermark_text_frame, width=20, height=1, wrap=tk.WORD)
watermark_text_entry.insert("1.0", WATERMARK_TEXT)
watermark_text_entry.grid(row=0, column=0, sticky=tk.W)

# دالة لتحديث المتغير عند تغيير النص
def update_watermark_text(event=None):
    global WATERMARK_TEXT
    WATERMARK_TEXT = watermark_text_entry.get("1.0", "end-1c")
    status_label.config(text=f"تم تغيير نص العلامة المائية إلى: {WATERMARK_TEXT}")
    root.update_idletasks()

# ربط حدث تغيير النص
watermark_text_entry.bind("<KeyRelease>", update_watermark_text)

# إضافة دعم للنسخ واللصق في حقل إدخال النص
def setup_entry_bindings(entry):
    """إعداد اختصارات لوحة المفاتيح للنسخ واللصق"""
    # تعريف دوال النسخ واللصق والقص
    def copy_text(event):
        if entry.selection_present():
            root.clipboard_clear()
            root.clipboard_append(entry.selection_get())
        return "break"  # منع السلوك الافتراضي

    def paste_text(event):
        try:
            text = root.clipboard_get()
            if entry.selection_present():
                entry.delete("sel.first", "sel.last")
            entry.insert("insert", text)
        except:
            pass
        return "break"  # منع السلوك الافتراضي

    def cut_text(event):
        if entry.selection_present():
            copy_text(event)
            entry.delete("sel.first", "sel.last")
        return "break"  # منع السلوك الافتراضي

    # ربط اختصارات لوحة المفاتيح
    entry.bind("<Control-c>", copy_text)
    entry.bind("<Control-v>", paste_text)
    entry.bind("<Control-x>", cut_text)

    # إضافة قائمة منبثقة للنقر بزر الماوس الأيمن
    popup_menu = tk.Menu(entry, tearoff=0)
    popup_menu.add_command(label="نسخ", command=lambda: copy_text(None))
    popup_menu.add_command(label="لصق", command=lambda: paste_text(None))
    popup_menu.add_command(label="قص", command=lambda: cut_text(None))

    def show_popup(event):
        try:
            popup_menu.tk_popup(event.x_root, event.y_root, 0)
        finally:
            popup_menu.grab_release()

    entry.bind("<Button-3>", show_popup)  # النقر بزر الماوس الأيمن

# إضافة دعم للنسخ واللصق في حقل النص
def setup_text_bindings(text_widget):
    """إعداد اختصارات لوحة المفاتيح للنسخ واللصق لعنصر Text"""
    # تعريف دوال النسخ واللصق والقص
    def copy_text(event=None):
        try:
            if text_widget.tag_ranges(tk.SEL):
                selected_text = text_widget.get(tk.SEL_FIRST, tk.SEL_LAST)
                root.clipboard_clear()
                root.clipboard_append(selected_text)
        except:
            pass
        return "break"  # منع السلوك الافتراضي

    def paste_text(event=None):
        try:
            text = root.clipboard_get()
            if text_widget.tag_ranges(tk.SEL):
                text_widget.delete(tk.SEL_FIRST, tk.SEL_LAST)
            text_widget.insert(tk.INSERT, text)
            update_watermark_text()  # تحديث المتغير بعد اللصق
        except:
            pass
        return "break"  # منع السلوك الافتراضي

    def cut_text(event=None):
        if copy_text():
            if text_widget.tag_ranges(tk.SEL):
                text_widget.delete(tk.SEL_FIRST, tk.SEL_LAST)
                update_watermark_text()  # تحديث المتغير بعد القص
        return "break"  # منع السلوك الافتراضي

    # ربط اختصارات لوحة المفاتيح
    text_widget.bind("<Control-c>", copy_text)
    text_widget.bind("<Control-v>", paste_text)
    text_widget.bind("<Control-x>", cut_text)

    # إضافة قائمة منبثقة للنقر بزر الماوس الأيمن
    popup_menu = tk.Menu(text_widget, tearoff=0)
    popup_menu.add_command(label="نسخ", command=copy_text)
    popup_menu.add_command(label="لصق", command=paste_text)
    popup_menu.add_command(label="قص", command=cut_text)

    def show_popup(event):
        try:
            popup_menu.tk_popup(event.x_root, event.y_root, 0)
        finally:
            popup_menu.grab_release()

    text_widget.bind("<Button-3>", show_popup)  # النقر بزر الماوس الأيمن

# تطبيق الإعدادات على حقل إدخال النص
setup_text_bindings(watermark_text_entry)

# ربط تغيير النص بالمتغير
watermark_text_var.trace_add('write', on_watermark_text_change)

# إضافة إطار للتحكم في شفافية وحجم العلامة المائية
watermark_controls_frame = ttk.Frame(watermark_frame)
watermark_controls_frame.grid(row=1, column=0, columnspan=5, sticky=(tk.W, tk.E), pady=5)

# التحكم في شفافية العلامة المائية
opacity_label = ttk.Label(watermark_controls_frame, text="الشفافية:", foreground="#2d3a4a")
opacity_label.grid(row=0, column=0, sticky=tk.W, pady=5)

def on_opacity_change(value):
    """تغيير شفافية العلامة المائية"""
    try:
        global WATERMARK_OPACITY
        WATERMARK_OPACITY = int(float(value))
        opacity_value_label.config(text=f"{WATERMARK_OPACITY}%")
        status_label.config(text=f"تم تغيير شفافية العلامة المائية إلى {WATERMARK_OPACITY}%")
        root.update_idletasks()
    except Exception as e:
        print(f"خطأ في تغيير شفافية العلامة المائية: {e}")

opacity_scale = ttk.Scale(watermark_controls_frame, from_=10, to=100, orient=tk.HORIZONTAL, command=on_opacity_change, value=WATERMARK_OPACITY)
opacity_scale.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=5)

opacity_value_label = ttk.Label(watermark_controls_frame, text=f"{WATERMARK_OPACITY}%", width=5)
opacity_value_label.grid(row=0, column=2, sticky=tk.W, pady=5)

# التحكم في حجم العلامة المائية
size_label = ttk.Label(watermark_controls_frame, text="الحجم:", foreground="#2d3a4a")
size_label.grid(row=0, column=3, sticky=tk.W, pady=5, padx=(20, 0))

def on_size_change(value):
    """تغيير حجم العلامة المائية"""
    try:
        global WATERMARK_SIZE_FACTOR
        WATERMARK_SIZE_FACTOR = float(value)
        size_value_label.config(text=f"{int(WATERMARK_SIZE_FACTOR * 100)}%")
        status_label.config(text=f"تم تغيير حجم العلامة المائية إلى {int(WATERMARK_SIZE_FACTOR * 100)}%")
        root.update_idletasks()
    except Exception as e:
        print(f"خطأ في تغيير حجم العلامة المائية: {e}")

size_scale = ttk.Scale(watermark_controls_frame, from_=0.3, to=1.0, orient=tk.HORIZONTAL, command=on_size_change, value=WATERMARK_SIZE_FACTOR)
size_scale.grid(row=0, column=4, sticky=(tk.W, tk.E), pady=5, padx=5)

size_value_label = ttk.Label(watermark_controls_frame, text=f"{int(WATERMARK_SIZE_FACTOR * 100)}%", width=5)
size_value_label.grid(row=0, column=5, sticky=tk.W, pady=5)

# شريط التقدم

progress_frame = ttk.Frame(main_frame)
progress_frame.grid(row=9, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)

# استبدال عناصر شريط التقدم والمُلصق بواجهة عصرية مع ظل للنص
replace_progress_and_yaser_labels()


# متغير عام لتخطي رسائل النجاح أثناء إنشاء فيديوهات متعددة
SKIP_SUCCESS_MESSAGES = False

# متغيرات عامة للتحكم في حالة الإيقاف
STOP_ALL_OPERATIONS = False
STOP_REQUESTED = False

# دالة لإيقاف جميع العمليات
def stop_all_operations():
    """إيقاف جميع العمليات الجارية"""
    global STOP_ALL_OPERATIONS, STOP_REQUESTED

    # تعطيل زر الإيقاف مؤقتاً لمنع الضغط المتكرر
    stop_button.config(state="disabled")

    # تحديث حالة التقدم فوراً
    status_label.config(text="جاري إيقاف جميع العمليات...")
    progress_bar["value"] = 0
    root.update_idletasks()

    # تعيين متغيرات الإيقاف
    STOP_REQUESTED = True
    STOP_ALL_OPERATIONS = True

    # إعادة تعيين حالة العملية
    if hasattr(export_all_translations, 'is_running'):
        export_all_translations.is_running = False

    # إيقاف جميع الخيوط النشطة (باستثناء الخيط الرئيسي)
    import threading
    import sys

    # إيقاف جميع العمليات الفرعية
    try:
        import psutil
        current_process = psutil.Process()
        for child in current_process.children(recursive=True):
            try:
                child.terminate()
            except:
                pass
    except:
        pass

    # حذف الملفات المؤقتة فوراً
    try:
        batch_mode_flag = os.path.join(EXEC_DIR, "batch_mode.flag")
        temp_info_path = os.path.join(EXEC_DIR, "temp_info.json")
        temp_files_path = os.path.join(EXEC_DIR, "temp_files.json")

        for file_path in [batch_mode_flag, temp_info_path, temp_files_path]:
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except:
                    pass
    except:
        pass

    # تحرير الذاكرة
    try:
        import gc
        gc.collect()
    except:
        pass

    # إعادة تفعيل واجهة المستخدم
    export_button.config(state="normal")
    export_all_button.config(state="normal")

    # تحديث حالة التقدم
    status_label.config(text="تم إيقاف جميع العمليات")

    # جدولة إعادة تفعيل زر الإيقاف بعد ثانية
    root.after(1000, lambda: stop_button.config(state="normal"))

    # تحديث الواجهة فوراً
    root.update_idletasks()

    # إعادة تعيين متغيرات الإيقاف بعد فترة
    root.after(2000, reset_stop_flags)

# دالة منفصلة لتنظيف الملفات المؤقتة
def cleanup_temp_files():
    """حذف الملفات المؤقتة"""
    try:
        batch_mode_flag = os.path.join(EXEC_DIR, "batch_mode.flag")
        temp_info_path = os.path.join(EXEC_DIR, "temp_info.json")
        temp_files_path = os.path.join(EXEC_DIR, "temp_files.json")

        for file_path in [batch_mode_flag, temp_info_path, temp_files_path]:
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except:
                    pass
    except Exception as e:
        print(f"خطأ في حذف الملفات المؤقتة: {e}")

    # إعادة تعيين متغير الإيقاف بعد فترة قصيرة
    root.after(1000, reset_stop_flags)

# دالة لإعادة تعيين متغيرات الإيقاف
def reset_stop_flags():
    """إعادة تعيين متغيرات الإيقاف"""
    global STOP_ALL_OPERATIONS, STOP_REQUESTED
    STOP_ALL_OPERATIONS = False
    STOP_REQUESTED = False

# دالة لإنشاء فيديوهات بكل اللغات المتاحة - نسخة محسنة لتقليل التهنيج
def export_all_translations():
    """إنشاء فيديوهات بكل اللغات المتاحة دفعة واحدة - نسخة محسنة لتقليل التهنيج"""
    global STOP_ALL_OPERATIONS, STOP_REQUESTED

    # التحقق من أن العملية ليست قيد التنفيذ بالفعل
    if hasattr(export_all_translations, "is_running") and export_all_translations.is_running:
        # استخدام root.after لتجنب تجميد الواجهة
        root.after(0, lambda: messagebox.showinfo("تنبيه", "هناك عملية أخرى قيد التنفيذ. يرجى الانتظار حتى تنتهي."))
        return

    # إعادة تعيين متغيرات الإيقاف
    STOP_ALL_OPERATIONS = False
    STOP_REQUESTED = False

    # تعيين علامة التشغيل
    export_all_translations.is_running = True

    # تفعيل زر الإيقاف وتعطيل أزرار الإنشاء
    stop_button.config(state="normal")
    export_button.config(state="disabled")
    export_all_button.config(state="disabled")

    # تحديث حالة التقدم
    status_label.config(text="جاري بدء العملية...")
    progress_bar["value"] = 0

    # تحديث الواجهة فوراً
    root.update_idletasks()

    # تحرير الذاكرة قبل بدء العملية
    import gc
    gc.collect(2)  # تنظيف جميع الأجيال

    # استخدام خيط منفصل لتنفيذ العملية لتجنب تجميد الواجهة
    def process_thread():
        try:
            # جدولة بدء العملية في الخيط الرئيسي
            root.after(100, start_export_process)
        except Exception as e:
            print(f"خطأ في بدء العملية: {e}")
            root.after(0, lambda: show_error_and_reset(f"حدث خطأ أثناء بدء العملية:\n{str(e)}"))

    # بدء خيط المعالجة
    import threading
    thread = threading.Thread(target=process_thread)
    thread.daemon = True
    thread.start()

# دالة لبدء عملية إنشاء الفيديوهات - نسخة محسنة لتقليل التهنيج
def start_export_process():
    """بدء عملية إنشاء الفيديوهات بكل اللغات - نسخة محسنة لتقليل التهنيج"""
    global STOP_ALL_OPERATIONS, STOP_REQUESTED

    # التحقق من حالة الإيقاف
    if STOP_REQUESTED or STOP_ALL_OPERATIONS:
        export_all_translations.is_running = False
        status_label.config(text="تم إلغاء العملية")
        root.update_idletasks()
        return

    # استخدام خيط منفصل لتنفيذ العملية لتجنب تجميد الواجهة
    def process_thread():
        try:
            # الحصول على معلومات السورة والآيات
            surah_idx = SURAH_NAMES.index(surah_var.get()) + 1
            start_ayah = int(start_ayah_var.get() or 1)
            end_ayah = int(end_ayah_var.get() or start_ayah)
            reciter_id = RECITERS_MAP.get(reciter_var.get(), "")

            # التحقق من صحة المدخلات
            if not surah_var.get() or not reciter_var.get():
                root.after(0, lambda: show_error_and_reset("يرجى اختيار السورة والقارئ"))
                return

            max_ayah = VERSE_COUNTS.get(surah_idx, 0)
            if start_ayah < 1 or end_ayah > max_ayah or start_ayah > end_ayah:
                root.after(0, lambda: show_error_and_reset(f"نطاق الآيات غير صحيح. يجب أن يكون بين 1 و {max_ayah}"))
                return

            # حفظ المعلومات في ملف مؤقت
            temp_info = {
                "surah_idx": surah_idx,
                "start_ayah": start_ayah,
                "end_ayah": end_ayah,
                "reciter_id": reciter_id,
                "reciter_display": reciter_var.get(),
                "surah_name": surah_var.get()
            }

            # حفظ المعلومات في ملف مؤقت
            import json
            try:
                with open(os.path.join(EXEC_DIR, "temp_info.json"), "w", encoding="utf-8") as f:
                    json.dump(temp_info, f, ensure_ascii=False)
            except Exception as e:
                # استخدام root.after لتجنب تجميد الواجهة
                root.after(0, lambda: show_error_and_reset(f"حدث خطأ أثناء حفظ المعلومات المؤقتة:\n{str(e)}"))
                return

            # تحديث حالة التقدم في الخيط الرئيسي
            root.after(0, lambda: status_label.config(text="جاري تنزيل الترجمات..."))
            root.after(0, lambda: root.update_idletasks())

            # جدولة تنزيل الترجمات في الخيط الرئيسي
            root.after(100, lambda: download_translations_async(surah_idx, start_ayah, end_ayah))
        except Exception as e:
            # استخدام root.after لتجنب تجميد الواجهة
            root.after(0, lambda: show_error_and_reset(f"حدث خطأ أثناء بدء العملية:\n{str(e)}"))

            # تحرير الذاكرة
            import gc
            gc.collect(2)

    # بدء خيط المعالجة
    import threading
    thread = threading.Thread(target=process_thread)
    thread.daemon = True
    thread.start()

    # تحديث الواجهة فوراً
    root.update_idletasks()

# دالة لتنزيل الترجمات بشكل غير متزامن - نسخة محسنة لتقليل التهنيج
def download_translations_async(surah_idx, start_ayah, end_ayah):
    """تنزيل الترجمات بشكل غير متزامن - نسخة محسنة لتقليل التهنيج"""
    global STOP_ALL_OPERATIONS, STOP_REQUESTED

    # التحقق من حالة الإيقاف
    if STOP_REQUESTED or STOP_ALL_OPERATIONS:
        root.after(0, lambda: show_error_and_reset("تم إلغاء العملية"))
        return

    # استخدام خيط منفصل لتنفيذ العملية لتجنب تجميد الواجهة
    def process_thread():
        try:
            # تحديث حالة التقدم في الخيط الرئيسي
            root.after(0, lambda: status_label.config(text="جاري تنزيل الترجمات..."))
            root.after(0, lambda: root.update_idletasks())

            # تنزيل الترجمات
            translations = download_translations(surah_idx, start_ayah, end_ayah)

            # التحقق من حالة الإيقاف
            if STOP_REQUESTED or STOP_ALL_OPERATIONS:
                root.after(0, lambda: show_error_and_reset("تم إلغاء العملية"))
                return

            # تحديث قائمة ملفات الترجمة في الخيط الرئيسي
            root.after(0, refresh_translation_files)

            # تأخير قصير للسماح بتحديث الواجهة
            import time
            time.sleep(0.1)

            # قائمة ملفات الترجمة المتاحة
            translation_files = get_translation_files()

            # إذا لم تكن هناك ملفات ترجمة، نعرض رسالة للمستخدم
            if not translation_files:
                root.after(0, lambda: show_error_and_reset("لا توجد ملفات ترجمة متاحة. تأكد من اتصالك بالإنترنت وحاول مرة أخرى."))
                return

            # تصفية ملفات الترجمة للغات المطلوبة فقط
            required_langs = ["ar", "en", "fr", "it", "ru", "tr"]
            filtered_files = []

            for file in translation_files:
                # التحقق من حالة الإيقاف
                if STOP_REQUESTED or STOP_ALL_OPERATIONS:
                    root.after(0, lambda: show_error_and_reset("تم إلغاء العملية"))
                    return

                file_name = os.path.basename(file)
                lang_code = ""

                if file_name.startswith("translation_") and "_" in file_name:
                    lang_parts = file_name.split("_")[1].split(".")
                    if lang_parts and len(lang_parts[0]) <= 3:
                        lang_code = lang_parts[0]

                if lang_code in required_langs:
                    filtered_files.append((file, lang_code))

            if not filtered_files:
                root.after(0, lambda: show_error_and_reset("لم يتم العثور على ملفات ترجمة للغات المطلوبة (ar, en, fr, it, ru, tr)."))
                return

            # حفظ قائمة الملفات في ملف مؤقت
            try:
                with open(os.path.join(EXEC_DIR, "temp_files.json"), "w", encoding="utf-8") as f:
                    json.dump(filtered_files, f, ensure_ascii=False)
            except Exception as e:
                root.after(0, lambda: show_error_and_reset(f"حدث خطأ أثناء حفظ قائمة الملفات:\n{str(e)}"))
                return

            # التحقق من حالة الإيقاف
            if STOP_REQUESTED or STOP_ALL_OPERATIONS:
                root.after(0, lambda: show_error_and_reset("تم إلغاء العملية"))
                return

            # تحديث حالة التقدم في الخيط الرئيسي
            root.after(0, lambda: status_label.config(text="جاري بدء معالجة الفيديوهات..."))
            root.after(0, lambda: root.update_idletasks())

            # إنشاء ملف علامة لتشغيل البرنامج في وضع معالجة الفيديوهات
            try:
                with open(os.path.join(EXEC_DIR, "batch_mode.flag"), "w") as f:
                    f.write("1")
            except Exception as e:
                root.after(0, lambda: show_error_and_reset(f"حدث خطأ أثناء إنشاء ملف العلامة:\n{str(e)}"))
                return

            # جدولة معالجة الفيديوهات في الخيط الرئيسي
            root.after(100, process_batch_videos)

            # تحرير الذاكرة
            import gc
            gc.collect(2)

        except Exception as e:
            root.after(0, lambda: show_error_and_reset(f"حدث خطأ أثناء تنزيل الترجمات:\n{str(e)}"))

            # تحرير الذاكرة
            import gc
            gc.collect(2)

    # بدء خيط المعالجة
    import threading
    thread = threading.Thread(target=process_thread)
    thread.daemon = True
    thread.start()

    # تحديث الواجهة فوراً
    root.update_idletasks()

# دالة لعرض رسالة خطأ وإعادة تعيين حالة العملية
def show_error_and_reset(error_message):
    """عرض رسالة خطأ وإعادة تعيين حالة العملية"""
    # إعادة تعيين حالة العملية
    export_all_translations.is_running = False

    # إعادة تفعيل أزرار الإنشاء
    export_button.config(state="normal")
    export_all_button.config(state="normal")

    # تحديث حالة التقدم
    status_label.config(text="حدث خطأ")
    progress_bar["value"] = 0

    # عرض رسالة الخطأ
    print(f"خطأ: {error_message}")

    # تحديث الواجهة فوراً
    root.update_idletasks()

    # دالة لإعادة تفعيل عناصر الواجهة
    def enable_ui():
        """إعادة تفعيل عناصر الواجهة"""
        export_button.config(state="normal")
        export_all_button.config(state="normal")
        stop_button.config(state="normal")
        status_label.config(text="جاهز")
        progress_bar["value"] = 0
        root.update_idletasks()
        # إعادة تعيين علامة التشغيل
        export_all_translations.is_running = False





# زر التصدير
def export_video():
    # تعطيل الأزرار أثناء إنشاء الفيديو
    export_button.config(state="disabled")
    export_all_button.config(state="disabled")

    # إعادة تعيين متغيرات الإيقاف
    global STOP_ALL_OPERATIONS, STOP_REQUESTED
    STOP_ALL_OPERATIONS = False
    STOP_REQUESTED = False

    # تحديث شريط التقدم
    update_progress("جاري بدء إنشاء الفيديو...", 0)

    # طباعة معلومات الخلفية للتشخيص
    global BACKGROUND_VIDEO_PATH
    print(f"معلومات الخلفية قبل إنشاء الفيديو: {BACKGROUND_VIDEO_PATH}")

    def on_video_complete():
        # إعادة تفعيل الأزرار بعد الانتهاء
        export_button.config(state="normal")
        export_all_button.config(state="normal")
        update_progress("تم الانتهاء من إنشاء الفيديو", 100)

        # تشغيل صوت تنبيه عند الانتهاء
        try:
            # استخدام صوت النظام للتنبيه
            import winsound
            winsound.MessageBeep(winsound.MB_ICONASTERISK)
        except Exception as e:
            print(f"خطأ في تشغيل صوت التنبيه: {e}")

    # إنشاء الفيديو في خيط منفصل
    def create_video_thread():
        try:
            # التحقق من وجود ملف الخلفية
            if BACKGROUND_VIDEO_PATH and os.path.exists(BACKGROUND_VIDEO_PATH):
                print(f"تم التأكد من وجود ملف الخلفية: {BACKGROUND_VIDEO_PATH}")
            else:
                print(f"تحذير: ملف الخلفية غير موجود أو لم يتم تحديده: {BACKGROUND_VIDEO_PATH}")

            # استدعاء دالة إنشاء الفيديو
            build_video(
                reciter_var.get(),
                surah_var.get(),
                int(start_ayah_var.get()),
                int(end_ayah_var.get()),
                translation_var.get(),
                translation_file_var.get() if translation_file_var.get() else None
            )
        except Exception as e:
            print(f"خطأ في إنشاء الفيديو: {e}")
            # استخدام root.after لتجنب تجميد الواجهة
            root.after(0, lambda: messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء الفيديو:\n{str(e)}"))
        finally:
            # استدعاء دالة إعادة تفعيل الأزرار في الخيط الرئيسي
            root.after(0, on_video_complete)

            # تحرير الذاكرة
            import gc
            gc.collect(2)

    # بدء خيط إنشاء الفيديو
    import threading
    thread = threading.Thread(target=create_video_thread)
    thread.daemon = True
    thread.start()

    # تحديث الواجهة فوراً
    root.update_idletasks()

# دالة لإدارة إعدادات خادم MCP
def open_mcp_settings():
    """فتح نافذة إعدادات خادم MCP"""
    global MCP_SERVERS, CURRENT_MCP_SERVER

    # إنشاء نافذة منبثقة
    settings_window = tk.Toplevel(root)
    settings_window.title("إعدادات خادم MCP")
    settings_window.geometry("800x600")
    settings_window.resizable(True, True)
    settings_window.transient(root)
    settings_window.grab_set()

    # تطبيق نفس النمط على النافذة المنبثقة
    settings_frame = ttk.Frame(settings_window, padding="10")
    settings_frame.pack(fill=tk.BOTH, expand=True)

    # عنوان النافذة
    title_label = ttk.Label(settings_frame, text="إعدادات خادم MCP", font=("Cairo", 16, "bold"))
    title_label.grid(row=0, column=0, columnspan=2, pady=10, sticky=tk.W)

    # إطار لعرض الخوادم المتاحة
    servers_frame = ttk.LabelFrame(settings_frame, text="الخوادم المتاحة")
    servers_frame.grid(row=1, column=0, padx=5, pady=5, sticky=(tk.N, tk.W, tk.E, tk.S))

    # قائمة الخوادم
    servers_listbox = tk.Listbox(servers_frame, width=30, height=10, font=("Cairo", 12))
    servers_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

    # شريط التمرير
    scrollbar = ttk.Scrollbar(servers_frame, orient=tk.VERTICAL, command=servers_listbox.yview)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    servers_listbox.config(yscrollcommand=scrollbar.set)

    # إطار لتفاصيل الخادم المحدد
    details_frame = ttk.LabelFrame(settings_frame, text="تفاصيل الخادم")
    details_frame.grid(row=1, column=1, padx=5, pady=5, sticky=(tk.N, tk.W, tk.E, tk.S))

    # حقول إدخال تفاصيل الخادم
    ttk.Label(details_frame, text="اسم الخادم:").grid(row=0, column=0, sticky=tk.W, pady=5)
    server_name_var = tk.StringVar()
    server_name_entry = ttk.Entry(details_frame, textvariable=server_name_var, width=40)
    server_name_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5)

    ttk.Label(details_frame, text="عنوان API القرآن:").grid(row=1, column=0, sticky=tk.W, pady=5)
    quran_api_var = tk.StringVar()
    quran_api_entry = ttk.Entry(details_frame, textvariable=quran_api_var, width=40)
    quran_api_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5)

    ttk.Label(details_frame, text="عنوان API الصوت:").grid(row=2, column=0, sticky=tk.W, pady=5)
    audio_api_var = tk.StringVar()
    audio_api_entry = ttk.Entry(details_frame, textvariable=audio_api_var, width=40)
    audio_api_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5)

    ttk.Label(details_frame, text="الأداء (1-1000):").grid(row=3, column=0, sticky=tk.W, pady=5)
    performance_var = tk.StringVar()
    performance_entry = ttk.Entry(details_frame, textvariable=performance_var, width=40)
    performance_entry.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5)

    ttk.Label(details_frame, text="الحد الأقصى للاتصالات:").grid(row=4, column=0, sticky=tk.W, pady=5)
    max_connections_var = tk.StringVar()
    max_connections_entry = ttk.Entry(details_frame, textvariable=max_connections_var, width=40)
    max_connections_entry.grid(row=4, column=1, sticky=(tk.W, tk.E), pady=5)

    ttk.Label(details_frame, text="مهلة الاتصال (ثوانٍ):").grid(row=5, column=0, sticky=tk.W, pady=5)
    timeout_var = tk.StringVar()
    timeout_entry = ttk.Entry(details_frame, textvariable=timeout_var, width=40)
    timeout_entry.grid(row=5, column=1, sticky=(tk.W, tk.E), pady=5)

    # خيارات إضافية
    is_default_var = tk.BooleanVar(value=False)
    is_default_check = ttk.Checkbutton(details_frame, text="خادم افتراضي", variable=is_default_var)
    is_default_check.grid(row=6, column=0, sticky=tk.W, pady=5)

    is_active_var = tk.BooleanVar(value=True)
    is_active_check = ttk.Checkbutton(details_frame, text="نشط", variable=is_active_var)
    is_active_check.grid(row=6, column=1, sticky=tk.W, pady=5)

    # إطار لأزرار التحكم
    control_frame = ttk.Frame(settings_frame)
    control_frame.grid(row=2, column=0, columnspan=2, pady=10)

    # حالة الاتصال
    status_var = tk.StringVar(value="جاهز")
    status_label = ttk.Label(settings_frame, textvariable=status_var, foreground="#008000")
    status_label.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

    # دالة لاختبار اتصال الخادم
    def test_server_connection():
        """اختبار اتصال الخادم المحدد"""
        try:
            status_var.set("جاري اختبار الاتصال...")
            status_label.config(foreground="#FF8C00")  # لون برتقالي للانتظار
            settings_window.update_idletasks()

            # الحصول على عناوين API من الحقول
            quran_api = quran_api_var.get().strip()
            audio_api = audio_api_var.get().strip()
            timeout = int(timeout_var.get().strip() or 10)

            # اختبار اتصال API القرآن
            if quran_api:
                if "alquran.cloud" in quran_api:
                    test_url = f"{quran_api}/ayah/1:1/quran-uthmani"
                elif "quran.com" in quran_api:
                    test_url = f"{quran_api}/verses/by_key/1:1"
                else:
                    test_url = f"{quran_api}/ayah/1:1/quran-uthmani"

                start_time = time.time()
                resp = requests.get(test_url, timeout=timeout)
                resp.raise_for_status()
                quran_api_time = time.time() - start_time

                # اختبار اتصال API الصوت
                if audio_api:
                    if "everyayah.com" in audio_api:
                        test_url = f"{audio_api}/Alafasy_64kbps/001001.mp3"
                    elif "quran.com" in audio_api:
                        test_url = f"{audio_api}/Alafasy_64kbps/verses/1_1"
                    else:
                        test_url = f"{audio_api}/Alafasy_64kbps/001001.mp3"

                    start_time = time.time()
                    resp = requests.get(test_url, timeout=timeout)
                    resp.raise_for_status()
                    audio_api_time = time.time() - start_time

                    # حساب متوسط وقت الاستجابة
                    avg_time = (quran_api_time + audio_api_time) / 2
                    performance = int(1000 / (avg_time * 10)) if avg_time > 0 else 100

                    # تحديث حقل الأداء
                    performance_var.set(str(performance))

                    status_var.set(f"تم الاتصال بنجاح! متوسط وقت الاستجابة: {avg_time:.2f} ثانية، الأداء: {performance}")
                    status_label.config(foreground="#008000")  # لون أخضر للنجاح
                else:
                    status_var.set(f"تم الاتصال بـ API القرآن بنجاح! وقت الاستجابة: {quran_api_time:.2f} ثانية")
                    status_label.config(foreground="#008000")  # لون أخضر للنجاح
            else:
                status_var.set("يرجى إدخال عنوان API القرآن على الأقل")
                status_label.config(foreground="#FF0000")  # لون أحمر للخطأ
        except Exception as e:
            status_var.set(f"فشل الاتصال: {str(e)}")
            status_label.config(foreground="#FF0000")  # لون أحمر للخطأ

    # دالة لتحديث قائمة الخوادم
    def update_servers_list():
        """تحديث قائمة الخوادم المعروضة"""
        servers_listbox.delete(0, tk.END)
        for i, server in enumerate(MCP_SERVERS):
            status = ""
            if server.get("is_default", False):
                status += "[افتراضي] "
            if not server.get("is_active", True):
                status += "[غير نشط] "
            if server.get("name") == CURRENT_MCP_SERVER.get("name"):
                status += "[الحالي] "

            servers_listbox.insert(tk.END, f"{status}{server.get('name', 'خادم بدون اسم')}")

            # تمييز الخادم الحالي
            if server.get("name") == CURRENT_MCP_SERVER.get("name"):
                servers_listbox.itemconfig(i, {'bg': '#e6f7ff'})

    # دالة لعرض تفاصيل الخادم المحدد
    def show_server_details(event=None):
        """عرض تفاصيل الخادم المحدد في القائمة"""
        try:
            selected_index = servers_listbox.curselection()[0]
            server = MCP_SERVERS[selected_index]

            server_name_var.set(server.get("name", ""))
            quran_api_var.set(server.get("quran_api", ""))
            audio_api_var.set(server.get("audio_api", ""))
            performance_var.set(str(server.get("performance", 100)))
            max_connections_var.set(str(server.get("max_connections", 5)))
            timeout_var.set(str(server.get("timeout", 10)))
            is_default_var.set(server.get("is_default", False))
            is_active_var.set(server.get("is_active", True))

            # تفعيل أزرار التعديل والحذف
            edit_button.config(state="normal")
            delete_button.config(state="normal")
            set_current_button.config(state="normal")
        except (IndexError, Exception) as e:
            print(f"خطأ في عرض تفاصيل الخادم: {e}")

    # دالة لإضافة خادم جديد
    def add_new_server():
        """إضافة خادم جديد إلى القائمة"""
        # مسح الحقول
        server_name_var.set("خادم جديد")
        quran_api_var.set("https://api.alquran.cloud/v1")
        audio_api_var.set("https://everyayah.com/data")
        performance_var.set("100")
        max_connections_var.set("5")
        timeout_var.set("10")
        is_default_var.set(False)
        is_active_var.set(True)

        # تعطيل أزرار التعديل والحذف
        edit_button.config(state="disabled")
        delete_button.config(state="disabled")
        set_current_button.config(state="disabled")

        # إلغاء تحديد أي خادم في القائمة
        servers_listbox.selection_clear(0, tk.END)

    # دالة لحفظ الخادم الجديد أو تعديل خادم موجود
    def save_server():
        """حفظ الخادم الجديد أو تعديل خادم موجود"""
        global MCP_SERVERS, CURRENT_MCP_SERVER

        try:
            # التحقق من صحة البيانات
            server_name = server_name_var.get().strip()
            quran_api = quran_api_var.get().strip()
            audio_api = audio_api_var.get().strip()

            if not server_name:
                status_var.set("يرجى إدخال اسم الخادم")
                status_label.config(foreground="#FF0000")
                return

            if not quran_api:
                status_var.set("يرجى إدخال عنوان API القرآن")
                status_label.config(foreground="#FF0000")
                return

            # إنشاء كائن الخادم
            server = {
                "name": server_name,
                "quran_api": quran_api,
                "audio_api": audio_api,
                "performance": int(performance_var.get() or 100),
                "max_connections": int(max_connections_var.get() or 5),
                "timeout": int(timeout_var.get() or 10),
                "is_default": is_default_var.get(),
                "is_active": is_active_var.get()
            }

            # التحقق مما إذا كان هذا تعديلاً لخادم موجود
            try:
                selected_index = servers_listbox.curselection()[0]
                # تعديل الخادم الموجود
                MCP_SERVERS[selected_index] = server

                # إذا كان هذا هو الخادم الحالي، قم بتحديثه
                if MCP_SERVERS[selected_index].get("name") == CURRENT_MCP_SERVER.get("name"):
                    CURRENT_MCP_SERVER = server

                status_var.set(f"تم تحديث الخادم {server_name} بنجاح")
            except (IndexError, Exception):
                # إضافة خادم جديد
                MCP_SERVERS.append(server)
                status_var.set(f"تم إضافة الخادم {server_name} بنجاح")

            # إذا كان هذا هو الخادم الافتراضي، قم بإلغاء تعيين الخوادم الأخرى كافتراضية
            if server.get("is_default", False):
                for s in MCP_SERVERS:
                    if s != server:
                        s["is_default"] = False

            # حفظ التغييرات في ملف التكوين
            save_mcp_config()

            # تحديث قائمة الخوادم
            update_servers_list()

            status_label.config(foreground="#008000")
        except Exception as e:
            status_var.set(f"خطأ في حفظ الخادم: {str(e)}")
            status_label.config(foreground="#FF0000")

    # دالة لحذف الخادم المحدد
    def delete_server():
        """حذف الخادم المحدد من القائمة"""
        global MCP_SERVERS, CURRENT_MCP_SERVER

        try:
            selected_index = servers_listbox.curselection()[0]
            server = MCP_SERVERS[selected_index]

            # التأكد من عدم حذف الخادم الحالي أو الافتراضي
            if server.get("name") == CURRENT_MCP_SERVER.get("name"):
                status_var.set("لا يمكن حذف الخادم الحالي. قم بتغيير الخادم الحالي أولاً.")
                status_label.config(foreground="#FF0000")
                return

            if server.get("is_default", False):
                status_var.set("لا يمكن حذف الخادم الافتراضي. قم بتعيين خادم آخر كافتراضي أولاً.")
                status_label.config(foreground="#FF0000")
                return

            # تأكيد الحذف
            if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف الخادم {server.get('name')}؟"):
                # حذف الخادم
                del MCP_SERVERS[selected_index]

                # حفظ التغييرات في ملف التكوين
                save_mcp_config()

                # تحديث قائمة الخوادم
                update_servers_list()

                # مسح الحقول
                add_new_server()

                status_var.set(f"تم حذف الخادم بنجاح")
                status_label.config(foreground="#008000")
        except (IndexError, Exception) as e:
            status_var.set(f"خطأ في حذف الخادم: {str(e)}")
            status_label.config(foreground="#FF0000")

    # دالة لتعيين الخادم المحدد كخادم حالي
    def set_current_server():
        """تعيين الخادم المحدد كخادم حالي"""
        global MCP_SERVERS, CURRENT_MCP_SERVER

        try:
            selected_index = servers_listbox.curselection()[0]
            server = MCP_SERVERS[selected_index]

            # التأكد من أن الخادم نشط
            if not server.get("is_active", True):
                status_var.set("لا يمكن تعيين خادم غير نشط كخادم حالي")
                status_label.config(foreground="#FF0000")
                return

            # تعيين الخادم كخادم حالي
            CURRENT_MCP_SERVER = server

            # حفظ التغييرات في ملف التكوين
            save_mcp_config()

            # تحديث قائمة الخوادم
            update_servers_list()

            status_var.set(f"تم تعيين {server.get('name')} كخادم حالي بنجاح")
            status_label.config(foreground="#008000")

            # تحديث الترجمات المتاحة
            fetch_translations()
        except (IndexError, Exception) as e:
            status_var.set(f"خطأ في تعيين الخادم الحالي: {str(e)}")
            status_label.config(foreground="#FF0000")

    # دالة لحفظ إعدادات الخادم في ملف التكوين
    def save_mcp_config():
        """حفظ إعدادات الخادم في ملف التكوين"""
        try:
            config = {
                "servers": MCP_SERVERS,
                "current_server": CURRENT_MCP_SERVER.get("name", "الخادم الافتراضي")
            }

            with open(os.path.join(base, "mcp_servers.json"), "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"خطأ في حفظ إعدادات الخادم: {e}")
            status_var.set(f"خطأ في حفظ إعدادات الخادم: {str(e)}")
            status_label.config(foreground="#FF0000")

    # أزرار التحكم
    add_button = ttk.Button(control_frame, text="إضافة خادم جديد", command=add_new_server)
    add_button.grid(row=0, column=0, padx=5, pady=5)

    save_button = ttk.Button(control_frame, text="حفظ", command=save_server)
    save_button.grid(row=0, column=1, padx=5, pady=5)

    edit_button = ttk.Button(control_frame, text="تعديل", command=lambda: show_server_details(), state="disabled")
    edit_button.grid(row=0, column=2, padx=5, pady=5)

    delete_button = ttk.Button(control_frame, text="حذف", command=delete_server, state="disabled")
    delete_button.grid(row=0, column=3, padx=5, pady=5)

    set_current_button = ttk.Button(control_frame, text="تعيين كخادم حالي", command=set_current_server, state="disabled")
    set_current_button.grid(row=0, column=4, padx=5, pady=5)

    test_button = ttk.Button(control_frame, text="اختبار الاتصال", command=test_server_connection)
    test_button.grid(row=0, column=5, padx=5, pady=5)

    # ربط حدث النقر المزدوج بعرض تفاصيل الخادم
    servers_listbox.bind("<Double-1>", show_server_details)
    # ربط حدث النقر المفرد بعرض تفاصيل الخادم
    servers_listbox.bind("<<ListboxSelect>>", show_server_details)

    # تحديث قائمة الخوادم
    update_servers_list()

    # تعيين حجم الإطارات
    settings_frame.columnconfigure(0, weight=1)
    settings_frame.columnconfigure(1, weight=2)
    settings_frame.rowconfigure(1, weight=1)

    # تركيز النافذة
    settings_window.focus_set()



# إنشاء إطار للأزرار
buttons_frame = ttk.Frame(main_frame)
buttons_frame.grid(row=10, column=0, columnspan=2, pady=10)

# زر إنشاء فيديو واحد
export_button = ttk.Button(buttons_frame, text="إنشاء الفيديو", command=export_video)
export_button.grid(row=0, column=0, padx=5)

# زر إنشاء فيديوهات بكل اللغات
export_all_button = ttk.Button(buttons_frame, text="إنشاء فيديوهات بكل اللغات", command=export_all_translations)
export_all_button.grid(row=0, column=1, padx=5)

# زر إنهاء العمليات
stop_button = ttk.Button(buttons_frame, text="إنهاء العمليات", command=stop_all_operations, style="Accent.TButton")
stop_button.grid(row=0, column=2, padx=5)

# زر إعدادات خادم MCP
mcp_settings_button = ttk.Button(buttons_frame, text="إعدادات خادم MCP", command=open_mcp_settings)
mcp_settings_button.grid(row=0, column=3, padx=5)

# تعريف نمط خاص لزر الإنهاء
style = ttk.Style()
style.configure("Accent.TButton", background="#ff5252", foreground="white", font=("Cairo", 10, "bold"))

# تحسين تنسيق النافذة
for child in main_frame.winfo_children():
    child.grid_configure(padx=5)

# جعل الأعمدة قابلة للتمدد
main_frame.columnconfigure(1, weight=1)

# حدث تغيير السورة
def on_surah_change(*args):
    """تحديث قيم الآيات عند تغيير السورة"""
    try:
        surah_idx = SURAH_NAMES.index(surah_var.get()) + 1
        max_ayah = VERSE_COUNTS[surah_idx]

        # تحديث قيمة آخر آية إذا كانت القيمة الحالية أكبر من العدد الكلي للآيات
        try:
            current_end = int(end_ayah_var.get())
            if current_end > max_ayah:
                end_ayah_var.set(str(max_ayah))
        except ValueError:
            # في حالة وجود قيمة غير صالحة، نعيد تعيين القيمة إلى 1
            end_ayah_var.set("1")

        # تحديث الواجهة فوراً
        root.update_idletasks()
    except Exception as e:
        print(f"خطأ في تحديث قيم الآيات: {e}")

# استخدام trace_add بدلاً من trace (الطريقة المهملة)
surah_var.trace_add('write', on_surah_change)

# ───────────────────────────────────────────────────────────────────────────────
# 1) تهيئة المكتبات والتحقق من المتطلبات
# ───────────────────────────────────────────────────────────────────────────────
def verify_dependencies():
    """التحقق من وجود جميع المتطلبات والمكتبات"""
    missing_deps = []

    # التحقق من FFmpeg
    if not os.path.exists(os.environ.get("FFMPEG_BINARY", "")):
        missing_deps.append("FFmpeg")

    # التحقق من ImageMagick
    if not os.path.exists(os.environ.get("IMAGEMAGICK_BINARY", "")):
        missing_deps.append("ImageMagick")

    # التحقق من ملف الخط
    if not os.path.exists(FONT_PATH):
        missing_deps.append("Font file (font.ttf)")

    # التحقق من المجلدات المطلوبة
    for dir_path in [OUT_DIR, AUDIO_DIR, VIDEO_DIR]:
        if not os.path.exists(dir_path):
            try:
                os.makedirs(dir_path, exist_ok=True)
            except Exception as e:
                missing_deps.append(f"Cannot create directory: {dir_path} ({str(e)})")

    return missing_deps

def init_environment():
    """تهيئة البيئة والتحقق من المتطلبات"""
    try:
        # تحديث مسارات النظام
        os.environ["PATH"] += os.pathsep + r"C:\ffmpeg"
        os.environ["FFMPEG_BINARY"] = r"C:\ffmpeg\ffmpeg.exe"
        os.environ["IMAGEIO_FFMPEG_EXE"] = r"C:\ffmpeg\ffmpeg.exe"
        os.environ["IMAGEMAGICK_BINARY"] = r"C:\Program Files\ImageMagick\magick.exe"

        # التحقق من المتطلبات
        missing = verify_dependencies()
        if missing:
            warning_msg = "تحذير: بعض المكونات مفقودة أو غير قابلة للوصول:\n- " + "\n- ".join(missing)
            print(warning_msg)
            # عرض تحذير بدلاً من خطأ
            messagebox.showwarning("تحذير في التهيئة", warning_msg + "\n\nقد لا تعمل بعض الوظائف بشكل صحيح.")
            # لا نقوم بإيقاف البرنامج، بل نستمر مع تعطيل بعض الوظائف

        # محاولة تهيئة المكتبات
        try:
            from pydub import AudioSegment
            AudioSegment.converter = os.environ["FFMPEG_BINARY"]
            AudioSegment.ffmpeg = os.environ["FFMPEG_BINARY"]
            AudioSegment.ffprobe = os.environ["FFMPEG_BINARY"]
        except Exception as e:
            print(f"تحذير: فشل تهيئة مكتبة PyDub: {e}")
            # لا نقوم بإيقاف البرنامج، بل نستمر مع تعطيل بعض الوظائف

        # جلب قائمة الترجمات
        try:
            fetch_translations()
        except Exception as e:
            print(f"تحذير: فشل جلب قائمة الترجمات: {e}")
            # لا نقوم بإيقاف البرنامج، بل نستمر مع الترجمات الافتراضية

        return True

    except Exception as e:
        print(f"تحذير: حدث خطأ أثناء تهيئة البيئة: {e}")
        messagebox.showwarning("تحذير في التهيئة", f"حدث خطأ أثناء تهيئة البيئة:\n{str(e)}\n\nقد لا تعمل بعض الوظائف بشكل صحيح.")
        return True  # نعيد True بدلاً من False لتجنب إغلاق البرنامج

# التحقق من البيئة عند بدء التشغيل - لا نقوم بإغلاق البرنامج حتى لو فشلت التهيئة
init_environment()

# دالة لمعالجة الفيديوهات في وضع الدفعة - نسخة محسنة لتقليل التهنيج
def process_batch_videos():
    """معالجة الفيديوهات في وضع الدفعة - نسخة محسنة لتقليل التهنيج"""
    import json
    import os
    global STOP_ALL_OPERATIONS, STOP_REQUESTED

    # إعادة تعيين متغيرات الإيقاف
    STOP_ALL_OPERATIONS = False
    STOP_REQUESTED = False

    # تفعيل زر الإيقاف
    stop_button.config(state="normal")

    # التحقق من وجود ملفات المعلومات المؤقتة
    temp_info_path = os.path.join(EXEC_DIR, "temp_info.json")
    temp_files_path = os.path.join(EXEC_DIR, "temp_files.json")
    batch_mode_flag = os.path.join(EXEC_DIR, "batch_mode.flag")

    # استخدام خيط منفصل لتنفيذ العملية لتجنب تجميد الواجهة
    def process_thread():
        try:
            if not os.path.exists(temp_info_path) or not os.path.exists(temp_files_path):
                # استخدام root.after لتجنب تجميد الواجهة
                root.after(0, lambda: messagebox.showerror("خطأ", "لم يتم العثور على ملفات المعلومات المؤقتة."))
                # حذف ملف العلامة
                if os.path.exists(batch_mode_flag):
                    try:
                        os.remove(batch_mode_flag)
                    except Exception as e:
                        print(f"خطأ في حذف ملف العلامة: {e}")
                # إعادة تعيين حالة العملية
                export_all_translations.is_running = False
                return False

            # قراءة المعلومات المؤقتة
            try:
                with open(temp_info_path, "r", encoding="utf-8") as f:
                    temp_info = json.load(f)
            except Exception as e:
                root.after(0, lambda: messagebox.showerror("خطأ", f"فشل قراءة ملف المعلومات المؤقتة: {e}"))
                # إعادة تعيين حالة العملية
                export_all_translations.is_running = False
                return False

            # قراءة قائمة الملفات
            try:
                with open(temp_files_path, "r", encoding="utf-8") as f:
                    filtered_files = json.load(f)
            except Exception as e:
                root.after(0, lambda: messagebox.showerror("خطأ", f"فشل قراءة ملف قائمة الملفات: {e}"))
                # إعادة تعيين حالة العملية
                export_all_translations.is_running = False
                return False

            # التحقق من وجود المعلومات المطلوبة
            required_keys = ["surah_idx", "start_ayah", "end_ayah", "reciter_id", "reciter_display", "surah_name"]
            for key in required_keys:
                if key not in temp_info:
                    root.after(0, lambda k=key: messagebox.showerror("خطأ", f"المعلومات المؤقتة غير مكتملة: {k} غير موجود."))
                    # حذف ملف العلامة
                    if os.path.exists(batch_mode_flag):
                        try:
                            os.remove(batch_mode_flag)
                        except Exception as e:
                            print(f"خطأ في حذف ملف العلامة: {e}")
                    # إعادة تعيين حالة العملية
                    export_all_translations.is_running = False
                    return False

            # استخدام شريط التقدم الموجود في الواجهة الرئيسية
            # تحديث التقدم في الخيط الرئيسي
            total_files = len(filtered_files)
            root.after(0, lambda: progress_bar.configure(maximum=total_files))

            # تعطيل أزرار الواجهة أثناء المعالجة في الخيط الرئيسي
            root.after(0, lambda: export_button.config(state="disabled"))
            root.after(0, lambda: export_all_button.config(state="disabled"))
            root.after(0, lambda: root.update_idletasks())

            # جدولة معالجة الفيديوهات في الخيط الرئيسي
            root.after(100, lambda: process_videos_async(filtered_files, temp_info, total_files, 0))
            return True

        except Exception as e:
            print(f"خطأ في معالجة الفيديوهات: {e}")
            root.after(0, lambda: messagebox.showerror("خطأ", f"حدث خطأ أثناء معالجة الفيديوهات:\n{str(e)}"))
            # إعادة تعيين حالة العملية
            export_all_translations.is_running = False
            return False

    # بدء خيط المعالجة
    import threading
    thread = threading.Thread(target=process_thread)
    thread.daemon = True
    thread.start()

    # تحديث الواجهة فوراً
    root.update_idletasks()

    return True

def process_videos_async(filtered_files, temp_info, total_files, current_index):
    """معالجة الفيديوهات بشكل غير متزامن - نسخة محسنة لتقليل التهنيج"""
    global STOP_ALL_OPERATIONS, STOP_REQUESTED

    # التحقق من حالة الإيقاف أولاً
    if STOP_REQUESTED or STOP_ALL_OPERATIONS:
        print("تم اكتشاف طلب إيقاف العملية")
        # إعادة تعيين حالة العملية
        export_all_translations.is_running = False
        # تحديث التقدم
        root.after(0, lambda: update_progress("تم إيقاف العملية", 0))
        # إعادة تفعيل أزرار الواجهة
        root.after(0, lambda: export_button.config(state="normal"))
        root.after(0, lambda: export_all_button.config(state="normal"))
        # تحديث الواجهة فوراً
        root.after(0, lambda: root.update_idletasks())
        return

    # التحقق من اكتمال المعالجة
    if current_index >= len(filtered_files):
        # انتهت المعالجة بنجاح
        root.after(0, lambda: finish_processing(total_files))
        return

    # الحصول على معلومات الملف الحالي
    file, lang_code = filtered_files[current_index]

    # تحديث التقدم في الخيط الرئيسي
    root.after(0, lambda: update_progress(f"جاري إنشاء فيديو للغة {lang_code} ({current_index+1}/{total_files})...", current_index))
    root.after(0, lambda: root.update_idletasks())

    # تعيين متغير لتخطي رسائل النجاح
    global SKIP_SUCCESS_MESSAGES
    SKIP_SUCCESS_MESSAGES = True

    # استخدام خيط منفصل لإنشاء الفيديو
    def create_video_thread():
        try:
            # التحقق من حالة الإيقاف مرة أخرى
            if STOP_REQUESTED or STOP_ALL_OPERATIONS:
                return

            # إنشاء الفيديو
            build_video(
                temp_info["reciter_display"],
                temp_info["surah_name"],
                temp_info["start_ayah"],
                temp_info["end_ayah"],
                None,  # لا نستخدم translation_display
                file
            )

            # تنظيف الذاكرة بشكل أكثر فعالية
            import gc

            # تنظيف الذاكرة المؤقتة لـ MoviePy
            try:
                from moviepy.video.io.ffmpeg_reader import ffmpeg_parse_infos
                if hasattr(ffmpeg_parse_infos, "cache"):
                    ffmpeg_parse_infos.cache.clear()
            except:
                pass

            # إجبار نظام Python على تحرير الذاكرة
            gc.collect(2)  # إجبار تنظيف الجيل الثاني من الكائنات

            # تحديث الواجهة لمنع التجميد في الخيط الرئيسي
            root.after(0, lambda: root.update_idletasks())

            # جدولة معالجة الملف التالي فقط إذا لم يتم طلب الإيقاف
            if not (STOP_REQUESTED or STOP_ALL_OPERATIONS):
                # إضافة تأخير أطول بين معالجة الفيديوهات لتقليل التهنيج
                root.after(500, lambda: process_videos_async(filtered_files, temp_info, total_files, current_index + 1))
        except Exception as e:
            if not (STOP_REQUESTED or STOP_ALL_OPERATIONS):
                print(f"خطأ في إنشاء فيديو {lang_code}: {e}")
                # جدولة معالجة الملف التالي رغم الخطأ
                root.after(500, lambda: process_videos_async(filtered_files, temp_info, total_files, current_index + 1))

    # بدء خيط إنشاء الفيديو
    import threading
    video_thread = threading.Thread(target=create_video_thread)
    video_thread.daemon = True
    video_thread.start()

    # تحديث الواجهة فوراً
    root.after(0, lambda: root.update_idletasks())

def finish_processing(total_files):
    """إنهاء عملية المعالجة بنجاح - نسخة محسنة لتقليل التهنيج"""
    global STOP_ALL_OPERATIONS, STOP_REQUESTED

    # تحديث التقدم في الخيط الرئيسي
    root.after(0, lambda: update_progress("تم إنشاء جميع الفيديوهات بنجاح!", total_files))

    # إعادة تعيين متغيرات الإيقاف
    STOP_ALL_OPERATIONS = False
    STOP_REQUESTED = False

    # إعادة تعيين حالة العملية
    export_all_translations.is_running = False

    # إعادة تفعيل أزرار الواجهة في الخيط الرئيسي
    root.after(0, lambda: export_button.config(state="normal"))
    root.after(0, lambda: export_all_button.config(state="normal"))
    root.after(0, lambda: stop_button.config(state="normal"))

    # تحديث حالة التقدم بشكل نهائي في الخيط الرئيسي
    root.after(0, lambda: status_label.config(text="تم إنشاء جميع الفيديوهات بنجاح"))

    # تحديث الواجهة فوراً
    root.after(0, lambda: root.update_idletasks())

    # تشغيل صوت تنبيه عند الانتهاء
    def play_sound():
        try:
            # استخدام صوت النظام للتنبيه
            import winsound
            winsound.MessageBeep(winsound.MB_ICONASTERISK)
        except Exception as e:
            print(f"خطأ في تشغيل صوت التنبيه: {e}")

    # تشغيل الصوت في خيط منفصل
    import threading
    sound_thread = threading.Thread(target=play_sound)
    sound_thread.daemon = True
    sound_thread.start()

    # جدولة حذف الملفات المؤقتة بعد فترة قصيرة
    root.after(1000, cleanup_batch_files)

    # تحرير الذاكرة
    import gc
    gc.collect(2)

def cleanup_batch_files():
    """حذف ملفات المعالجة المؤقتة - نسخة محسنة لتقليل التهنيج"""
    # استخدام خيط منفصل لحذف الملفات المؤقتة لتجنب تجميد الواجهة
    def cleanup_thread():
        try:
            # تأخير قصير قبل بدء التنظيف
            import time
            time.sleep(0.5)

            batch_mode_flag = os.path.join(EXEC_DIR, "batch_mode.flag")
            temp_info_path = os.path.join(EXEC_DIR, "temp_info.json")
            temp_files_path = os.path.join(EXEC_DIR, "temp_files.json")

            # حذف الملفات المؤقتة
            for file_path in [temp_info_path, temp_files_path, batch_mode_flag]:
                if os.path.exists(file_path):
                    try:
                        os.remove(file_path)
                        print(f"تم حذف الملف المؤقت: {file_path}")
                    except Exception as e:
                        print(f"خطأ في حذف الملف {file_path}: {e}")

            # تحرير الذاكرة
            import gc
            gc.collect(2)

            # تحديث حالة التقدم في الخيط الرئيسي
            root.after(0, lambda: status_label.config(text="جاهز للعمليات الجديدة"))
            root.after(0, lambda: root.update_idletasks())

        except Exception as e:
            print(f"خطأ في حذف ملفات المعالجة المؤقتة: {e}")
            # تحديث حالة التقدم في الخيط الرئيسي في حالة الخطأ
            root.after(0, lambda: status_label.config(text="حدث خطأ في تنظيف الملفات المؤقتة"))
            root.after(0, lambda: root.update_idletasks())

    # بدء خيط التنظيف
    import threading
    thread = threading.Thread(target=cleanup_thread)
    thread.daemon = True
    thread.start()

    # تحديث الواجهة فوراً
    root.after(0, lambda: root.update_idletasks())

# التحقق من وجود ملف العلامة batch_mode.flag
batch_mode_flag = os.path.join(EXEC_DIR, "batch_mode.flag")
temp_info_path = os.path.join(EXEC_DIR, "temp_info.json")
temp_files_path = os.path.join(EXEC_DIR, "temp_files.json")

# التحقق من وجود جميع الملفات المطلوبة
if os.path.exists(batch_mode_flag):
    if not os.path.exists(temp_info_path) or not os.path.exists(temp_files_path):
        # حذف ملف العلامة إذا كانت الملفات المطلوبة غير موجودة
        try:
            os.remove(batch_mode_flag)
            print("تم حذف ملف batch_mode.flag لأن ملفات المعلومات المؤقتة غير موجودة")
        except Exception as e:
            print(f"خطأ في حذف ملف batch_mode.flag: {e}")
    else:
        # سؤال المستخدم قبل معالجة الفيديوهات في وضع الدفعة
        if messagebox.askyesno("تأكيد", "تم العثور على ملف batch_mode.flag. هل تريد بدء معالجة الفيديوهات تلقائيًا؟"):
            # تأخير بدء المعالجة لضمان تهيئة الواجهة بشكل كامل
            root.after(2000, process_batch_videos)
        else:
            # حذف ملف العلامة إذا اختار المستخدم عدم المتابعة
            try:
                os.remove(batch_mode_flag)
            except Exception as e:
                print(f"خطأ في حذف ملف batch_mode.flag: {e}")

# تشغيل حلقة الحدث الرئيسية
root.mainloop()